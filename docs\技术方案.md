# PromptForge 技术方案设计文档

## 1. 系统架构概述

### 1.1 整体架构
PromptForge 采用现代化的微服务架构，基于云原生技术栈构建，确保系统的可扩展性、可维护性和高可用性。

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层                                │
├─────────────────────────────────────────────────────────────┤
│  Web App (React)  │  Mobile App  │  VS Code Extension      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      API 网关层                             │
├─────────────────────────────────────────────────────────────┤
│  Nginx/Kong  │  负载均衡  │  限流  │  认证  │  监控         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      微服务层                               │
├─────────────────────────────────────────────────────────────┤
│ 用户服务 │ Prompt服务 │ 版本控制 │ 测试服务 │ 分析服务      │
│ 团队服务 │ 模型服务   │ 通知服务 │ 文件服务 │ 审计服务      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      数据层                                 │
├─────────────────────────────────────────────────────────────┤
│ PostgreSQL │ Redis │ MongoDB │ MinIO │ Elasticsearch        │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 架构原则
- **微服务化**: 按业务领域拆分服务，独立部署和扩展
- **云原生**: 基于容器化和 Kubernetes 部署
- **API 优先**: 所有功能通过 RESTful API 提供
- **事件驱动**: 使用消息队列实现服务间异步通信
- **数据一致性**: 采用最终一致性模型

## 2. 技术栈选择

### 2.1 前端技术栈
- **框架**: React 18 + TypeScript
- **状态管理**: Redux Toolkit + RTK Query
- **UI 组件库**: Ant Design / Material-UI
- **编辑器**: Monaco Editor (VS Code 编辑器内核)
- **构建工具**: Vite
- **测试**: Jest + React Testing Library
- **代码质量**: ESLint + Prettier + Husky

### 2.2 后端技术栈
- **运行时**: Node.js 18+ / Python 3.11+
- **框架**: Express.js / FastAPI
- **语言**: TypeScript / Python
- **API 文档**: OpenAPI 3.0 + Swagger
- **认证**: JWT + OAuth 2.0
- **消息队列**: Redis + Bull Queue
- **缓存**: Redis
- **搜索**: Elasticsearch

### 2.3 数据库设计
- **主数据库**: PostgreSQL 15
- **缓存**: Redis 7
- **文档存储**: MongoDB (可选)
- **对象存储**: MinIO / AWS S3
- **时序数据**: InfluxDB (监控数据)

### 2.4 基础设施
- **容器化**: Docker + Docker Compose
- **编排**: Kubernetes
- **CI/CD**: GitHub Actions / GitLab CI
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **追踪**: Jaeger

## 3. 核心服务设计

### 3.1 用户服务 (User Service)
**职责**: 用户认证、授权、个人信息管理

**核心功能**:
- 用户注册、登录、注销
- 第三方登录集成 (Google, GitHub, Microsoft)
- JWT Token 管理
- 用户个人信息管理
- 密码重置和安全设置

**技术实现**:
- 使用 Passport.js 处理多种认证策略
- bcrypt 进行密码加密
- Redis 存储会话和 Token 黑名单
- 邮件服务集成 (SendGrid/AWS SES)

### 3.2 Prompt 服务 (Prompt Service)
**职责**: Prompt 的 CRUD 操作、模板管理、搜索

**核心功能**:
- Prompt 创建、编辑、删除、查询
- 模板管理和复用
- 标签和分类管理
- 全文搜索和过滤
- Prompt 导入导出

**技术实现**:
- PostgreSQL 存储 Prompt 元数据
- Elasticsearch 提供全文搜索
- MinIO 存储大型 Prompt 文件
- 支持 Markdown 和富文本格式

### 3.3 版本控制服务 (Version Control Service)
**职责**: Prompt 版本管理、变更追踪、分支合并

**核心功能**:
- Git-like 版本控制
- 分支创建和管理
- 变更历史追踪
- 版本比较和回滚
- 合并冲突处理

**技术实现**:
- 自研轻量级版本控制系统
- 使用 diff 算法计算变更
- PostgreSQL 存储版本树结构
- 支持三方合并算法

### 3.4 模型服务 (Model Service)
**职责**: LLM 模型集成、API 调用、响应处理

**核心功能**:
- 多 LLM 提供商集成
- 统一 API 接口
- 请求路由和负载均衡
- 响应缓存和优化
- 成本统计和控制

**技术实现**:
- 适配器模式统一不同 LLM API
- 连接池管理 HTTP 连接
- Redis 缓存常用响应
- 异步处理长时间请求

### 3.5 测试服务 (Testing Service)
**职责**: Prompt 测试、A/B 测试、性能评估

**核心功能**:
- 单次和批量测试
- A/B 测试管理
- 测试结果分析
- 性能指标收集
- 自动化测试流程

**技术实现**:
- 队列系统处理批量测试
- 统计算法分析测试结果
- InfluxDB 存储时序性能数据
- 支持自定义评估指标

## 4. 数据库设计

### 4.1 核心数据模型

#### 用户相关表
```sql
-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    avatar_url TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- 团队表
CREATE TABLE teams (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    owner_id UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 团队成员表
CREATE TABLE team_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    team_id UUID REFERENCES teams(id),
    user_id UUID REFERENCES users(id),
    role VARCHAR(50) NOT NULL, -- owner, admin, member, viewer
    joined_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(team_id, user_id)
);
```

#### Prompt 相关表
```sql
-- Prompt 表
CREATE TABLE prompts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    content TEXT NOT NULL,
    tags TEXT[], -- PostgreSQL 数组类型
    category VARCHAR(100),
    language VARCHAR(10) DEFAULT 'en',
    owner_id UUID REFERENCES users(id),
    team_id UUID REFERENCES teams(id),
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Prompt 版本表
CREATE TABLE prompt_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    prompt_id UUID REFERENCES prompts(id),
    version_number INTEGER NOT NULL,
    content TEXT NOT NULL,
    change_message TEXT,
    author_id UUID REFERENCES users(id),
    parent_version_id UUID REFERENCES prompt_versions(id),
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(prompt_id, version_number)
);

-- Prompt 模板表
CREATE TABLE prompt_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    content TEXT NOT NULL,
    variables JSONB, -- 模板变量定义
    category VARCHAR(100),
    is_system BOOLEAN DEFAULT false,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 测试相关表
```sql
-- 测试会话表
CREATE TABLE test_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    prompt_id UUID REFERENCES prompts(id),
    prompt_version_id UUID REFERENCES prompt_versions(id),
    name VARCHAR(255),
    model_config JSONB, -- 模型配置参数
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

-- 测试用例表
CREATE TABLE test_cases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES test_sessions(id),
    input_data JSONB,
    expected_output TEXT,
    actual_output TEXT,
    execution_time INTEGER, -- 毫秒
    tokens_used INTEGER,
    cost DECIMAL(10,6),
    status VARCHAR(50), -- pending, running, completed, failed
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);
```

### 4.2 索引策略
```sql
-- 性能优化索引
CREATE INDEX idx_prompts_owner_team ON prompts(owner_id, team_id);
CREATE INDEX idx_prompts_tags ON prompts USING GIN(tags);
CREATE INDEX idx_prompts_category ON prompts(category);
CREATE INDEX idx_prompt_versions_prompt_id ON prompt_versions(prompt_id);
CREATE INDEX idx_test_sessions_prompt ON test_sessions(prompt_id);
CREATE INDEX idx_test_cases_session ON test_cases(session_id);

-- 全文搜索索引
CREATE INDEX idx_prompts_search ON prompts USING GIN(to_tsvector('english', name || ' ' || description || ' ' || content));
```

## 5. API 设计

### 5.1 API 设计原则
- **RESTful**: 遵循 REST 架构风格
- **版本化**: 使用 URL 版本控制 (/api/v1/)
- **统一响应**: 标准化的响应格式
- **错误处理**: 详细的错误码和消息
- **分页**: 支持游标和偏移分页
- **过滤排序**: 支持灵活的查询参数

### 5.2 核心 API 端点

#### 认证 API
```
POST /api/v1/auth/register     # 用户注册
POST /api/v1/auth/login        # 用户登录
POST /api/v1/auth/logout       # 用户登出
POST /api/v1/auth/refresh      # 刷新 Token
POST /api/v1/auth/forgot       # 忘记密码
POST /api/v1/auth/reset        # 重置密码
```

#### Prompt API
```
GET    /api/v1/prompts              # 获取 Prompt 列表
POST   /api/v1/prompts              # 创建 Prompt
GET    /api/v1/prompts/{id}         # 获取 Prompt 详情
PUT    /api/v1/prompts/{id}         # 更新 Prompt
DELETE /api/v1/prompts/{id}         # 删除 Prompt
POST   /api/v1/prompts/{id}/fork    # Fork Prompt
GET    /api/v1/prompts/search       # 搜索 Prompt
```

#### 版本控制 API
```
GET    /api/v1/prompts/{id}/versions        # 获取版本列表
POST   /api/v1/prompts/{id}/versions        # 创建新版本
GET    /api/v1/prompts/{id}/versions/{ver}  # 获取特定版本
POST   /api/v1/prompts/{id}/versions/{ver}/restore  # 恢复版本
GET    /api/v1/prompts/{id}/versions/compare # 版本比较
```

#### 测试 API
```
POST   /api/v1/test/single          # 单次测试
POST   /api/v1/test/batch           # 批量测试
GET    /api/v1/test/sessions        # 获取测试会话
GET    /api/v1/test/sessions/{id}   # 获取测试详情
POST   /api/v1/test/ab              # A/B 测试
```

### 5.3 响应格式标准
```json
{
  "success": true,
  "data": {
    // 响应数据
  },
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "uuid"
}

// 错误响应
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "输入参数验证失败",
    "details": {
      "field": "email",
      "reason": "格式不正确"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "uuid"
}
```

## 6. 安全设计

### 6.1 认证和授权
- **JWT Token**: 无状态认证机制
- **RBAC**: 基于角色的访问控制
- **OAuth 2.0**: 第三方登录集成
- **API Key**: 程序化访问支持
- **多因素认证**: 可选的 2FA 支持

### 6.2 数据安全
- **传输加密**: HTTPS/TLS 1.3
- **存储加密**: 数据库字段级加密
- **密码安全**: bcrypt + salt
- **敏感数据**: 单独加密存储
- **数据脱敏**: 日志和监控数据脱敏

### 6.3 API 安全
- **限流**: 基于用户和 IP 的限流
- **CORS**: 跨域资源共享控制
- **输入验证**: 严格的输入参数验证
- **SQL 注入**: 使用 ORM 和参数化查询
- **XSS 防护**: 输出编码和 CSP 头

## 7. 性能优化

### 7.1 缓存策略
- **Redis 缓存**: 热点数据缓存
- **CDN**: 静态资源分发
- **浏览器缓存**: 合理的缓存头设置
- **查询缓存**: 数据库查询结果缓存
- **应用缓存**: 内存级别的应用缓存

### 7.2 数据库优化
- **索引优化**: 合理的索引设计
- **查询优化**: SQL 查询性能调优
- **连接池**: 数据库连接池管理
- **读写分离**: 主从数据库架构
- **分库分表**: 大数据量的水平拆分

### 7.3 前端优化
- **代码分割**: 按路由和组件分割
- **懒加载**: 图片和组件懒加载
- **压缩**: Gzip/Brotli 压缩
- **预加载**: 关键资源预加载
- **Service Worker**: 离线缓存支持

## 8. 监控和运维

### 8.1 监控体系
- **应用监控**: APM 工具监控应用性能
- **基础设施监控**: 服务器和容器监控
- **业务监控**: 关键业务指标监控
- **日志监控**: 集中化日志收集和分析
- **告警系统**: 多渠道告警通知

### 8.2 部署策略
- **容器化**: Docker 容器部署
- **编排**: Kubernetes 集群管理
- **CI/CD**: 自动化构建和部署
- **蓝绿部署**: 零停机部署策略
- **回滚机制**: 快速回滚能力

### 8.3 备份和恢复
- **数据备份**: 定期自动备份
- **增量备份**: 减少备份时间和存储
- **异地备份**: 多地域备份策略
- **恢复测试**: 定期恢复演练
- **灾难恢复**: 完整的 DR 方案
