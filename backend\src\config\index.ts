import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

interface Config {
  // 服务器配置
  server: {
    port: number;
    host: string;
    env: string;
  };
  
  // 数据库配置
  database: {
    url: string;
  };
  
  // Redis 配置
  redis: {
    url: string;
    password?: string;
  };
  

  
  // 加密配置
  bcrypt: {
    rounds: number;
  };
  
  // CORS 配置
  cors: {
    origin: string | string[];
  };
  
  // 限流配置
  rateLimit: {
    windowMs: number;
    maxRequests: number;
  };
  
  // 文件上传配置
  upload: {
    maxFileSize: number;
    uploadPath: string;
    allowedFileTypes: string[];
  };
  
  // LLM API 配置
  llm: {
    openai: {
      apiKey?: string;
      baseUrl: string;
    };
    anthropic: {
      apiKey?: string;
    };
    google: {
      apiKey?: string;
    };
  };
  
  // 邮件配置
  smtp: {
    host: string;
    port: number;
    user: string;
    pass: string;
    from: string;
  };
  
  // MinIO 配置
  minio: {
    endpoint: string;
    accessKey: string;
    secretKey: string;
    bucket: string;
    useSSL: boolean;
  };
  
  // Elasticsearch 配置
  elasticsearch: {
    url: string;
    index: string;
  };
  
  // 监控配置
  monitoring: {
    sentryDsn?: string;
    logLevel: string;
  };
  
  // 第三方登录配置
  oauth: {
    google: {
      clientId?: string;
      clientSecret?: string;
    };
    github: {
      clientId?: string;
      clientSecret?: string;
    };
  };
  
  // 缓存配置
  cache: {
    ttl: number;
    maxSize: number;
  };
  
  // API 文档配置
  swagger: {
    enabled: boolean;
    path: string;
  };

  // JWT 配置
  jwt: {
    accessSecret: string;
    refreshSecret: string;
    accessExpiresIn: string;
    refreshExpiresIn: string;
  };

  // 应用配置
  app: {
    name: string;
    version: string;
  };
}

const config: Config = {
  server: {
    port: parseInt(process.env.PORT || '3001', 10),
    host: process.env.HOST || 'localhost',
    env: process.env.NODE_ENV || 'development',
  },
  
  database: {
    url: process.env.DATABASE_URL || 'postgresql://promptforge:promptforge123@localhost:5432/promptforge',
  },
  
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    password: process.env.REDIS_PASSWORD,
  },
  

  
  bcrypt: {
    rounds: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
  },
  
  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  },
  
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10),
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
  },
  
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760', 10),
    uploadPath: process.env.UPLOAD_PATH || 'uploads',
    allowedFileTypes: (process.env.ALLOWED_FILE_TYPES || '.txt,.md,.json,.csv').split(','),
  },
  
  llm: {
    openai: {
      apiKey: process.env.OPENAI_API_KEY,
      baseUrl: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
    },
    anthropic: {
      apiKey: process.env.ANTHROPIC_API_KEY,
    },
    google: {
      apiKey: process.env.GOOGLE_API_KEY,
    },
  },
  
  smtp: {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587', 10),
    user: process.env.SMTP_USER || '',
    pass: process.env.SMTP_PASS || '',
    from: process.env.SMTP_FROM || '<EMAIL>',
  },
  
  minio: {
    endpoint: process.env.MINIO_ENDPOINT || 'localhost:9000',
    accessKey: process.env.MINIO_ACCESS_KEY || 'minioadmin',
    secretKey: process.env.MINIO_SECRET_KEY || 'minioadmin123',
    bucket: process.env.MINIO_BUCKET || 'promptforge',
    useSSL: process.env.MINIO_USE_SSL === 'true',
  },
  
  elasticsearch: {
    url: process.env.ELASTICSEARCH_URL || 'http://localhost:9200',
    index: process.env.ELASTICSEARCH_INDEX || 'promptforge',
  },
  
  monitoring: {
    sentryDsn: process.env.SENTRY_DSN,
    logLevel: process.env.LOG_LEVEL || 'info',
  },
  
  oauth: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    },
    github: {
      clientId: process.env.GITHUB_CLIENT_ID,
      clientSecret: process.env.GITHUB_CLIENT_SECRET,
    },
  },
  
  cache: {
    ttl: parseInt(process.env.CACHE_TTL || '3600', 10),
    maxSize: parseInt(process.env.CACHE_MAX_SIZE || '100', 10),
  },
  
  swagger: {
    enabled: process.env.SWAGGER_ENABLED !== 'false',
    path: process.env.SWAGGER_PATH || '/api-docs',
  },

  jwt: {
    accessSecret: process.env.JWT_ACCESS_SECRET || 'your-super-secret-access-key-change-in-production',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key-change-in-production',
    accessExpiresIn: process.env.JWT_ACCESS_EXPIRES_IN || '15m',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  },

  app: {
    name: process.env.APP_NAME || 'PromptForge',
    version: process.env.APP_VERSION || '1.0.0',
  },
};

// 验证必需的环境变量
const requiredEnvVars: string[] = [];

if (config.server.env === 'production') {
  requiredEnvVars.push('DATABASE_URL');
}

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

export default config;
