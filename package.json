{"name": "promptforge", "version": "0.1.0", "description": "专业的 LLM Prompt 管理平台", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm start", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test -- --watchAll=false", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "npm run lint:fix:backend && npm run lint:fix:frontend", "lint:fix:backend": "cd backend && npm run lint:fix", "lint:fix:frontend": "cd frontend && npm run lint:fix", "format": "npm run format:backend && npm run format:frontend", "format:backend": "cd backend && npm run format", "format:frontend": "cd frontend && npm run format", "format:check": "npm run format:check:backend && npm run format:check:frontend", "format:check:backend": "cd backend && npm run format:check", "format:check:frontend": "cd frontend && npm run format:check", "install:all": "npm install && npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "clean": "npm run clean:backend && npm run clean:frontend", "clean:backend": "cd backend && npm run clean", "clean:frontend": "cd frontend && npm run clean", "docker:dev": "docker-compose up -d", "docker:prod": "docker-compose -f docker-compose.prod.yml up -d", "docker:stop": "docker-compose down", "docker:clean": "docker-compose down -v && docker system prune -f", "prepare": "husky install"}, "keywords": ["llm", "prompt", "ai", "management", "collaboration", "version-control"], "author": "PromptForge Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/promptforge.git"}, "bugs": {"url": "https://github.com/your-org/promptforge/issues"}, "homepage": "https://github.com/your-org/promptforge#readme", "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "workspaces": ["frontend", "backend", "shared"], "dependencies": {"compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "helmet": "^8.1.0", "morgan": "^1.10.1", "tsconfig-paths": "^4.2.0"}}