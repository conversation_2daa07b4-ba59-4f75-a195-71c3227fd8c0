import React from 'react';
import { <PERSON>, Typo<PERSON>, Button, Space } from 'antd';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { logout } from '../store/slices/authSlice';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);

  const handleLogout = () => {
    dispatch(logout());
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={2}>欢迎来到 PromptForge</Title>
            <Text type="secondary">
              您的智能提示词管理平台
            </Text>
          </div>
          
          {user && (
            <div>
              <Title level={4}>用户信息</Title>
              <p><strong>姓名:</strong> {user.name}</p>
              <p><strong>邮箱:</strong> {user.email}</p>
              <p><strong>角色:</strong> {user.role}</p>
              <p><strong>注册时间:</strong> {new Date(user.createdAt).toLocaleString()}</p>
            </div>
          )}
          
          <div>
            <Button type="primary" danger onClick={handleLogout}>
              退出登录
            </Button>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default Dashboard;
