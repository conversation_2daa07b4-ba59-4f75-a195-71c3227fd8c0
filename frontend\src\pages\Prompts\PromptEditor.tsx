import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Button,
  Select,
  Tag,
  Space,
  Row,
  Col,
  Divider,
  message,
  Spin,
  Switch,
  InputNumber,
  Tooltip,
  Typography,
} from 'antd';
import {
  SaveOutlined,
  EyeOutlined,
  ArrowLeftOutlined,
  InfoCircleOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import Editor from '@monaco-editor/react';
import { useAppDispatch, useAppSelector } from '@/hooks/redux';
import { promptApi } from '@/services/api';
import { Prompt, ModelConfig, CreatePromptInput, UpdatePromptInput } from '@/types';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

interface PromptEditorProps {}

const PromptEditor: React.FC<PromptEditorProps> = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [form] = Form.useForm();

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [prompt, setPrompt] = useState<Prompt | null>(null);
  const [content, setContent] = useState('');
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [showModelConfig, setShowModelConfig] = useState(false);

  const isEditMode = Boolean(id && id !== 'new');

  // 加载 Prompt 数据（编辑模式）
  useEffect(() => {
    if (isEditMode) {
      loadPrompt();
    } else {
      // 新建模式，设置默认值
      form.setFieldsValue({
        language: 'en',
        category: 'General',
        isPublic: false,
        isTemplate: false,
        modelConfig: {
          provider: 'openai',
          model: 'gpt-4',
          temperature: 0.7,
          maxTokens: 1000,
        },
      });
      setContent('');
    }
  }, [id, isEditMode]);

  const loadPrompt = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const response = await promptApi.getPromptById(id);
      if (response.success) {
        const promptData = response.data.prompt;
        setPrompt(promptData);
        setContent(promptData.content);

        // 设置表单值
        form.setFieldsValue({
          name: promptData.name,
          description: promptData.description,
          tags: promptData.tags,
          category: promptData.category,
          language: promptData.language,
          isPublic: promptData.isPublic,
          isTemplate: promptData.isTemplate,
          modelConfig: promptData.modelConfig || {
            provider: 'openai',
            model: 'gpt-4',
            temperature: 0.7,
            maxTokens: 1000,
          },
        });
      }
    } catch (error) {
      console.error('Failed to load prompt:', error);
      message.error('加载 Prompt 失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存 Prompt
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setSaving(true);

      const promptData = {
        ...values,
        content,
      };

      let response;
      if (isEditMode) {
        response = await promptApi.updatePrompt(id!, promptData as UpdatePromptInput);
      } else {
        response = await promptApi.createPrompt(promptData as CreatePromptInput);
      }

      if (response.success) {
        message.success(isEditMode ? 'Prompt 更新成功' : 'Prompt 创建成功');
        if (!isEditMode) {
          // 新建成功后跳转到编辑页面
          navigate(`/prompts/${response.data.prompt.id}/edit`);
        } else {
          // 更新本地数据
          setPrompt(response.data.prompt);
        }
      }
    } catch (error) {
      console.error('Failed to save prompt:', error);
      message.error('保存失败');
    } finally {
      setSaving(false);
    }
  };

  // 处理内容变化
  const handleContentChange = useCallback((value: string | undefined) => {
    setContent(value || '');
  }, []);

  // 返回列表
  const handleBack = () => {
    navigate('/prompts');
  };

  // 预览模式切换
  const togglePreview = () => {
    setIsPreviewMode(!isPreviewMode);
  };

  // 模型配置选项
  const modelProviders = [
    { value: 'openai', label: 'OpenAI' },
    { value: 'anthropic', label: 'Anthropic' },
    { value: 'google', label: 'Google' },
    { value: 'local', label: 'Local' },
  ];

  const modelOptions = {
    openai: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
    anthropic: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
    google: ['gemini-pro', 'gemini-pro-vision'],
    local: ['llama-2', 'mistral-7b'],
  };

  // 分类选项
  const categoryOptions = [
    'General',
    'Development',
    'Business',
    'Creative',
    'Analysis',
    'Education',
    'Research',
    'Marketing',
    'Support',
    'Other',
  ];

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      {/* 头部操作栏 */}
      <div style={{ marginBottom: '24px' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
                返回列表
              </Button>
              <Title level={3} style={{ margin: 0 }}>
                {isEditMode ? '编辑 Prompt' : '创建 Prompt'}
              </Title>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<EyeOutlined />}
                onClick={togglePreview}
                type={isPreviewMode ? 'primary' : 'default'}
              >
                {isPreviewMode ? '编辑模式' : '预览模式'}
              </Button>
              <Button
                icon={<SettingOutlined />}
                onClick={() => setShowModelConfig(!showModelConfig)}
                type={showModelConfig ? 'primary' : 'default'}
              >
                模型配置
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={saving}
                onClick={handleSave}
              >
                保存
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      <Form form={form} layout="vertical">
        <Row gutter={24}>
          {/* 左侧：基本信息 */}
          <Col xs={24} lg={8}>
            <Card title="基本信息" style={{ marginBottom: '24px' }}>
              <Form.Item
                name="name"
                label="名称"
                rules={[{ required: true, message: '请输入 Prompt 名称' }]}
              >
                <Input placeholder="输入 Prompt 名称" />
              </Form.Item>

              <Form.Item name="description" label="描述">
                <TextArea
                  rows={3}
                  placeholder="输入 Prompt 描述"
                  showCount
                  maxLength={500}
                />
              </Form.Item>

              <Form.Item name="tags" label="标签">
                <Select
                  mode="tags"
                  placeholder="添加标签"
                  style={{ width: '100%' }}
                  tokenSeparators={[',']}
                />
              </Form.Item>

              <Form.Item name="category" label="分类">
                <Select placeholder="选择分类">
                  {categoryOptions.map(category => (
                    <Option key={category} value={category}>
                      {category}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item name="language" label="语言">
                <Select placeholder="选择语言">
                  <Option value="en">English</Option>
                  <Option value="zh">中文</Option>
                  <Option value="ja">日本語</Option>
                  <Option value="ko">한국어</Option>
                  <Option value="fr">Français</Option>
                  <Option value="de">Deutsch</Option>
                  <Option value="es">Español</Option>
                </Select>
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="isPublic" label="公开" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="isTemplate" label="模板" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* 右侧：编辑器 */}
          <Col xs={24} lg={16}>
            <Card
              title={
                <Space>
                  <span>Prompt 内容</span>
                  <Tooltip title="支持变量语法：{variable_name}">
                    <InfoCircleOutlined />
                  </Tooltip>
                </Space>
              }
              style={{ marginBottom: '24px' }}
            >
              {isPreviewMode ? (
                <div
                  style={{
                    minHeight: '400px',
                    padding: '16px',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    backgroundColor: '#fafafa',
                    whiteSpace: 'pre-wrap',
                    fontFamily: 'monospace',
                  }}
                >
                  {content || '暂无内容'}
                </div>
              ) : (
                <Editor
                  height="400px"
                  defaultLanguage="markdown"
                  value={content}
                  onChange={handleContentChange}
                  options={{
                    minimap: { enabled: false },
                    wordWrap: 'on',
                    lineNumbers: 'on',
                    scrollBeyondLastLine: false,
                    automaticLayout: true,
                    fontSize: 14,
                    tabSize: 2,
                    insertSpaces: true,
                    renderWhitespace: 'selection',
                    bracketPairColorization: { enabled: true },
                  }}
                  theme="vs-light"
                />
              )}
            </Card>

            {/* 模型配置 */}
            {showModelConfig && (
              <Card title="模型配置" style={{ marginBottom: '24px' }}>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item name={['modelConfig', 'provider']} label="提供商">
                      <Select placeholder="选择模型提供商">
                        {modelProviders.map(provider => (
                          <Option key={provider.value} value={provider.value}>
                            {provider.label}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={['modelConfig', 'model']}
                      label="模型"
                      dependencies={[['modelConfig', 'provider']]}
                    >
                      <Select placeholder="选择模型">
                        {form.getFieldValue(['modelConfig', 'provider']) &&
                          modelOptions[form.getFieldValue(['modelConfig', 'provider']) as keyof typeof modelOptions]?.map(model => (
                            <Option key={model} value={model}>
                              {model}
                            </Option>
                          ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      name={['modelConfig', 'temperature']}
                      label={
                        <Space>
                          <span>Temperature</span>
                          <Tooltip title="控制输出的随机性，0-1之间，值越高越随机">
                            <InfoCircleOutlined />
                          </Tooltip>
                        </Space>
                      }
                    >
                      <InputNumber
                        min={0}
                        max={1}
                        step={0.1}
                        style={{ width: '100%' }}
                        placeholder="0.7"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name={['modelConfig', 'maxTokens']}
                      label={
                        <Space>
                          <span>Max Tokens</span>
                          <Tooltip title="最大输出token数量">
                            <InfoCircleOutlined />
                          </Tooltip>
                        </Space>
                      }
                    >
                      <InputNumber
                        min={1}
                        max={4000}
                        style={{ width: '100%' }}
                        placeholder="1000"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name={['modelConfig', 'topP']}
                      label={
                        <Space>
                          <span>Top P</span>
                          <Tooltip title="核采样参数，0-1之间">
                            <InfoCircleOutlined />
                          </Tooltip>
                        </Space>
                      }
                    >
                      <InputNumber
                        min={0}
                        max={1}
                        step={0.1}
                        style={{ width: '100%' }}
                        placeholder="1.0"
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={['modelConfig', 'frequencyPenalty']}
                      label={
                        <Space>
                          <span>Frequency Penalty</span>
                          <Tooltip title="频率惩罚，-2到2之间">
                            <InfoCircleOutlined />
                          </Tooltip>
                        </Space>
                      }
                    >
                      <InputNumber
                        min={-2}
                        max={2}
                        step={0.1}
                        style={{ width: '100%' }}
                        placeholder="0"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={['modelConfig', 'presencePenalty']}
                      label={
                        <Space>
                          <span>Presence Penalty</span>
                          <Tooltip title="存在惩罚，-2到2之间">
                            <InfoCircleOutlined />
                          </Tooltip>
                        </Space>
                      }
                    >
                      <InputNumber
                        min={-2}
                        max={2}
                        step={0.1}
                        style={{ width: '100%' }}
                        placeholder="0"
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item name={['modelConfig', 'stopSequences']} label="停止序列">
                  <Select
                    mode="tags"
                    placeholder="输入停止序列"
                    style={{ width: '100%' }}
                    tokenSeparators={[',']}
                  />
                </Form.Item>
              </Card>
            )}
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default PromptEditor;
