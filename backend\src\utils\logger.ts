import { createLogger, format, transports } from 'winston';
import config from '../config';

// 自定义日志格式
const logFormat = format.combine(
  format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  format.errors({ stack: true }),
  format.json(),
  format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    if (Object.keys(meta).length > 0) {
      log += ` ${JSON.stringify(meta)}`;
    }
    
    if (stack) {
      log += `\n${stack}`;
    }
    
    return log;
  })
);

// 创建 logger 实例
const logger = createLogger({
  level: config.monitoring.logLevel,
  format: logFormat,
  defaultMeta: { service: 'promptforge-backend' },
  transports: [
    // 控制台输出
    new transports.Console({
      format: format.combine(
        format.colorize(),
        format.simple()
      )
    }),
    
    // 错误日志文件
    new transports.File({
      filename: 'logs/error.log',
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    
    // 所有日志文件
    new transports.File({
      filename: 'logs/combined.log',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
  ],
  
  // 异常处理
  exceptionHandlers: [
    new transports.File({ filename: 'logs/exceptions.log' })
  ],
  
  // 拒绝处理
  rejectionHandlers: [
    new transports.File({ filename: 'logs/rejections.log' })
  ]
});

// 生产环境不输出到控制台
if (config.server.env === 'production') {
  logger.remove(logger.transports[0]);
}

// 导出日志方法
export const log = {
  error: (message: string, meta?: any) => logger.error(message, meta),
  warn: (message: string, meta?: any) => logger.warn(message, meta),
  info: (message: string, meta?: any) => logger.info(message, meta),
  debug: (message: string, meta?: any) => logger.debug(message, meta),
  verbose: (message: string, meta?: any) => logger.verbose(message, meta),
};

export default logger;
