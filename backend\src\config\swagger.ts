import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { Express } from 'express';
import config from '.';

// Swagger 配置选项
const swaggerOptions: swaggerJsdoc.Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'PromptForge API',
      version: '1.0.0',
      description: 'PromptForge LLM Prompt 管理平台 API 文档',
      contact: {
        name: 'PromptForge Team',
        email: '<EMAIL>',
        url: 'https://promptforge.com',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    servers: [
      {
        url: `http://${config.server.host}:${config.server.port}/api/v1`,
        description: '开发环境',
      },
      {
        url: 'https://api.promptforge.com/v1',
        description: '生产环境',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT 认证令牌',
        },
        apiKey: {
          type: 'apiKey',
          in: 'header',
          name: 'X-API-Key',
          description: 'API 密钥',
        },
      },
      schemas: {
        // 通用响应模式
        ApiResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              description: '请求是否成功',
            },
            data: {
              description: '响应数据',
            },
            message: {
              type: 'string',
              description: '响应消息',
            },
            error: {
              $ref: '#/components/schemas/ApiError',
            },
            timestamp: {
              type: 'string',
              format: 'date-time',
              description: '响应时间戳',
            },
            requestId: {
              type: 'string',
              description: '请求 ID',
            },
          },
        },
        
        // 错误响应模式
        ApiError: {
          type: 'object',
          properties: {
            code: {
              type: 'string',
              description: '错误代码',
            },
            message: {
              type: 'string',
              description: '错误消息',
            },
            details: {
              description: '错误详情',
            },
          },
        },
        
        // 分页响应模式
        PaginatedResponse: {
          type: 'object',
          properties: {
            items: {
              type: 'array',
              description: '数据项',
            },
            total: {
              type: 'integer',
              description: '总数量',
            },
            page: {
              type: 'integer',
              description: '当前页码',
            },
            pageSize: {
              type: 'integer',
              description: '每页大小',
            },
            totalPages: {
              type: 'integer',
              description: '总页数',
            },
          },
        },
        
        // 用户模式
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: '用户 ID',
            },
            email: {
              type: 'string',
              format: 'email',
              description: '邮箱地址',
            },
            username: {
              type: 'string',
              description: '用户名',
            },
            firstName: {
              type: 'string',
              description: '名字',
            },
            lastName: {
              type: 'string',
              description: '姓氏',
            },
            avatarUrl: {
              type: 'string',
              format: 'uri',
              description: '头像 URL',
            },
            isActive: {
              type: 'boolean',
              description: '是否激活',
            },
            isVerified: {
              type: 'boolean',
              description: '是否已验证',
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: '创建时间',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: '更新时间',
            },
          },
        },
        
        // Prompt 模式
        Prompt: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Prompt ID',
            },
            name: {
              type: 'string',
              description: 'Prompt 名称',
            },
            description: {
              type: 'string',
              description: 'Prompt 描述',
            },
            content: {
              type: 'string',
              description: 'Prompt 内容',
            },
            tags: {
              type: 'array',
              items: {
                type: 'string',
              },
              description: '标签',
            },
            category: {
              type: 'string',
              description: '分类',
            },
            language: {
              type: 'string',
              description: '语言',
            },
            modelConfig: {
              $ref: '#/components/schemas/ModelConfig',
            },
            ownerId: {
              type: 'string',
              description: '所有者 ID',
            },
            teamId: {
              type: 'string',
              description: '团队 ID',
            },
            isPublic: {
              type: 'boolean',
              description: '是否公开',
            },
            isTemplate: {
              type: 'boolean',
              description: '是否为模板',
            },
            versionCount: {
              type: 'integer',
              description: '版本数量',
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: '创建时间',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: '更新时间',
            },
          },
        },
        
        // 模型配置模式
        ModelConfig: {
          type: 'object',
          properties: {
            provider: {
              type: 'string',
              enum: ['openai', 'anthropic', 'google', 'local'],
              description: '模型提供商',
            },
            model: {
              type: 'string',
              description: '模型名称',
            },
            temperature: {
              type: 'number',
              minimum: 0,
              maximum: 2,
              description: '温度参数',
            },
            maxTokens: {
              type: 'integer',
              minimum: 1,
              description: '最大令牌数',
            },
            topP: {
              type: 'number',
              minimum: 0,
              maximum: 1,
              description: 'Top-p 参数',
            },
            frequencyPenalty: {
              type: 'number',
              minimum: -2,
              maximum: 2,
              description: '频率惩罚',
            },
            presencePenalty: {
              type: 'number',
              minimum: -2,
              maximum: 2,
              description: '存在惩罚',
            },
            stopSequences: {
              type: 'array',
              items: {
                type: 'string',
              },
              description: '停止序列',
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
    tags: [
      {
        name: 'Authentication',
        description: '认证相关接口',
      },
      {
        name: 'Users',
        description: '用户管理接口',
      },
      {
        name: 'Teams',
        description: '团队管理接口',
      },
      {
        name: 'Prompts',
        description: 'Prompt 管理接口',
      },
      {
        name: 'Testing',
        description: '测试相关接口',
      },
      {
        name: 'Analytics',
        description: '分析统计接口',
      },
    ],
  },
  apis: [
    './src/routes/*.ts',
    './src/controllers/*.ts',
  ],
};

// 生成 Swagger 规范
const swaggerSpec = swaggerJsdoc(swaggerOptions);

// Swagger UI 配置
const swaggerUiOptions = {
  explorer: true,
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    filter: true,
    showExtensions: true,
    showCommonExtensions: true,
  },
  customCss: `
    .swagger-ui .topbar { display: none }
    .swagger-ui .info { margin: 20px 0 }
    .swagger-ui .scheme-container { margin: 20px 0 }
  `,
  customSiteTitle: 'PromptForge API Documentation',
};

// 设置 Swagger 文档
export const setupSwagger = (app: Express): void => {
  if (config.swagger.enabled) {
    app.use(
      config.swagger.path,
      swaggerUi.serve,
      swaggerUi.setup(swaggerSpec, swaggerUiOptions)
    );
    
    // 提供 JSON 格式的 API 规范
    app.get('/api-spec.json', (_req, res) => {
      res.setHeader('Content-Type', 'application/json');
      res.send(swaggerSpec);
    });
    
    console.log(`📚 Swagger documentation available at: http://${config.server.host}:${config.server.port}${config.swagger.path}`);
  }
};

export { swaggerSpec };
export default swaggerUi;
