import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Team, TeamMember } from '@/types';
import { teamApi } from '@/services/api';

interface TeamState {
  teams: Team[];
  currentTeam: Team | null;
  teamMembers: TeamMember[];
  isLoading: boolean;
  error: string | null;
}

// 异步 actions
export const fetchTeams = createAsyncThunk('team/fetchTeams', async (_, { rejectWithValue }) => {
  try {
    const response = await teamApi.getTeams();
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error.response?.data?.message || 'Failed to fetch teams');
  }
});

export const fetchTeamById = createAsyncThunk(
  'team/fetchTeamById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await teamApi.getTeamById(id);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch team');
    }
  },
);

export const createTeam = createAsyncThunk(
  'team/createTeam',
  async (teamData: Partial<Team>, { rejectWithValue }) => {
    try {
      const response = await teamApi.createTeam(teamData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create team');
    }
  },
);

export const updateTeam = createAsyncThunk(
  'team/updateTeam',
  async ({ id, data }: { id: string; data: Partial<Team> }, { rejectWithValue }) => {
    try {
      const response = await teamApi.updateTeam(id, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update team');
    }
  },
);

export const deleteTeam = createAsyncThunk(
  'team/deleteTeam',
  async (id: string, { rejectWithValue }) => {
    try {
      await teamApi.deleteTeam(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete team');
    }
  },
);

export const fetchTeamMembers = createAsyncThunk(
  'team/fetchTeamMembers',
  async (teamId: string, { rejectWithValue }) => {
    try {
      const response = await teamApi.getTeamMembers(teamId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch team members');
    }
  },
);

export const addTeamMember = createAsyncThunk(
  'team/addTeamMember',
  async (
    { teamId, email, role }: { teamId: string; email: string; role: string },
    { rejectWithValue },
  ) => {
    try {
      const response = await teamApi.addTeamMember(teamId, { email, role });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add team member');
    }
  },
);

export const updateTeamMemberRole = createAsyncThunk(
  'team/updateTeamMemberRole',
  async (
    { teamId, memberId, role }: { teamId: string; memberId: string; role: string },
    { rejectWithValue },
  ) => {
    try {
      const response = await teamApi.updateTeamMemberRole(teamId, memberId, role);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update member role');
    }
  },
);

export const removeTeamMember = createAsyncThunk(
  'team/removeTeamMember',
  async ({ teamId, memberId }: { teamId: string; memberId: string }, { rejectWithValue }) => {
    try {
      await teamApi.removeTeamMember(teamId, memberId);
      return memberId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to remove team member');
    }
  },
);

// 初始状态
const initialState: TeamState = {
  teams: [],
  currentTeam: null,
  teamMembers: [],
  isLoading: false,
  error: null,
};

// Slice
const teamSlice = createSlice({
  name: 'team',
  initialState,
  reducers: {
    clearError: state => {
      state.error = null;
    },
    setCurrentTeam: (state, action: PayloadAction<Team | null>) => {
      state.currentTeam = action.payload;
    },
    clearCurrentTeam: state => {
      state.currentTeam = null;
      state.teamMembers = [];
    },
  },
  extraReducers: builder => {
    // Fetch teams
    builder
      .addCase(fetchTeams.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTeams.fulfilled, (state, action) => {
        state.isLoading = false;
        state.teams = action.payload;
      })
      .addCase(fetchTeams.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch team by ID
    builder
      .addCase(fetchTeamById.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTeamById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentTeam = action.payload;
      })
      .addCase(fetchTeamById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create team
    builder
      .addCase(createTeam.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createTeam.fulfilled, (state, action) => {
        state.isLoading = false;
        state.teams.unshift(action.payload);
        state.currentTeam = action.payload;
      })
      .addCase(createTeam.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update team
    builder
      .addCase(updateTeam.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateTeam.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.teams.findIndex(t => t.id === action.payload.id);
        if (index !== -1) {
          state.teams[index] = action.payload;
        }
        if (state.currentTeam?.id === action.payload.id) {
          state.currentTeam = action.payload;
        }
      })
      .addCase(updateTeam.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Delete team
    builder
      .addCase(deleteTeam.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteTeam.fulfilled, (state, action) => {
        state.isLoading = false;
        state.teams = state.teams.filter(t => t.id !== action.payload);
        if (state.currentTeam?.id === action.payload) {
          state.currentTeam = null;
        }
      })
      .addCase(deleteTeam.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch team members
    builder
      .addCase(fetchTeamMembers.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTeamMembers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.teamMembers = action.payload;
      })
      .addCase(fetchTeamMembers.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Add team member
    builder
      .addCase(addTeamMember.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(addTeamMember.fulfilled, (state, action) => {
        state.isLoading = false;
        state.teamMembers.push(action.payload);
      })
      .addCase(addTeamMember.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update team member role
    builder
      .addCase(updateTeamMemberRole.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateTeamMemberRole.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.teamMembers.findIndex(m => m.id === action.payload.id);
        if (index !== -1) {
          state.teamMembers[index] = action.payload;
        }
      })
      .addCase(updateTeamMemberRole.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Remove team member
    builder
      .addCase(removeTeamMember.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(removeTeamMember.fulfilled, (state, action) => {
        state.isLoading = false;
        state.teamMembers = state.teamMembers.filter(m => m.id !== action.payload);
      })
      .addCase(removeTeamMember.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setCurrentTeam, clearCurrentTeam } = teamSlice.actions;

export default teamSlice.reducer;
