import React, { useEffect, useState } from 'react';
import { Spin } from 'antd';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { getCurrentUser, initializeAuth, refreshToken } from '../../store/slices/authSlice';

interface AuthInitializerProps {
  children: React.ReactNode;
}

const AuthInitializer: React.FC<AuthInitializerProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const { accessToken, refreshToken: refreshTokenValue } = useAppSelector(state => state.auth);
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    const initializeAuthentication = async () => {
      try {
        // 初始化认证状态（从 localStorage 恢复令牌）
        dispatch(initializeAuth());

        // 如果有访问令牌，尝试获取用户信息
        if (accessToken) {
          try {
            await dispatch(getCurrentUser()).unwrap();
          } catch (error) {
            console.log('Access token expired, trying to refresh...');
            
            // 如果获取用户信息失败，尝试刷新令牌
            if (refreshTokenValue) {
              try {
                await dispatch(refreshToken()).unwrap();
                // 刷新成功后再次获取用户信息
                await dispatch(getCurrentUser()).unwrap();
              } catch (refreshError) {
                console.log('Refresh token also expired, user needs to login again');
                // 刷新令牌也失败了，用户需要重新登录
              }
            }
          }
        }
      } catch (error) {
        console.error('Authentication initialization failed:', error);
      } finally {
        setIsInitializing(false);
      }
    };

    initializeAuthentication();
  }, [dispatch, accessToken, refreshTokenValue]);

  // 如果正在初始化，显示加载状态
  if (isInitializing) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <Spin size="large" />
        <div style={{ color: '#666', fontSize: '16px' }}>
          正在初始化应用...
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default AuthInitializer;
