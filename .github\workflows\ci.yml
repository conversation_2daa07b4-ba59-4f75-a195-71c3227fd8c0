name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  # 代码质量检查
  lint:
    name: Code Quality Check
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: |
        if [ -d "frontend" ]; then
          cd frontend && npm ci
        fi
        if [ -d "backend" ]; then
          cd backend && npm ci
        fi
        
    - name: Run ESLint (Frontend)
      if: hashFiles('frontend/package.json') != ''
      run: cd frontend && npm run lint
      
    - name: Run ESLint (Backend)
      if: hashFiles('backend/package.json') != ''
      run: cd backend && npm run lint
      
    - name: Check code formatting
      run: |
        if [ -d "frontend" ]; then
          cd frontend && npm run format:check
        fi
        if [ -d "backend" ]; then
          cd backend && npm run format:check
        fi

  # 前端测试
  frontend-test:
    name: Frontend Tests
    runs-on: ubuntu-latest
    if: hashFiles('frontend/package.json') != ''
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Install dependencies
      run: cd frontend && npm ci
      
    - name: Run tests
      run: cd frontend && npm test -- --coverage --watchAll=false
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  # 后端测试
  backend-test:
    name: Backend Tests
    runs-on: ubuntu-latest
    if: hashFiles('backend/package.json') != ''
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: promptforge_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json
        
    - name: Install dependencies
      run: cd backend && npm ci
      
    - name: Run database migrations
      run: cd backend && npm run migrate
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/promptforge_test
        
    - name: Run tests
      run: cd backend && npm test -- --coverage
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/promptforge_test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret-key
        
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage/lcov.info
        flags: backend
        name: backend-coverage

  # 安全扫描
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # 构建测试
  build:
    name: Build Test
    runs-on: ubuntu-latest
    needs: [lint, frontend-test, backend-test]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: |
        if [ -d "frontend" ]; then
          cd frontend && npm ci
        fi
        if [ -d "backend" ]; then
          cd backend && npm ci
        fi
        
    - name: Build frontend
      if: hashFiles('frontend/package.json') != ''
      run: cd frontend && npm run build
      
    - name: Build backend
      if: hashFiles('backend/package.json') != ''
      run: cd backend && npm run build
      
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-artifacts
        path: |
          frontend/build/
          backend/dist/
        retention-days: 7

  # Docker 构建
  docker-build:
    name: Docker Build
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Login to Docker Hub
      if: github.ref == 'refs/heads/main'
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
        
    - name: Build and push frontend image
      if: hashFiles('frontend/Dockerfile') != ''
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        push: ${{ github.ref == 'refs/heads/main' }}
        tags: |
          promptforge/frontend:latest
          promptforge/frontend:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
    - name: Build and push backend image
      if: hashFiles('backend/Dockerfile') != ''
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        push: ${{ github.ref == 'refs/heads/main' }}
        tags: |
          promptforge/backend:latest
          promptforge/backend:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 部署到测试环境
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [docker-build]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to staging
      run: |
        echo "部署到测试环境"
        # 这里添加实际的部署脚本
        
  # 部署到生产环境
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [docker-build]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to production
      run: |
        echo "部署到生产环境"
        # 这里添加实际的部署脚本
