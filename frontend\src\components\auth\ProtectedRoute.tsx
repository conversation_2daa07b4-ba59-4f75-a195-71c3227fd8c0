import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { getCurrentUser, initializeAuth } from '../../store/slices/authSlice';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requiredRole?: string;
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  requiredRole,
  redirectTo = '/auth/login',
}) => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const { user, accessToken, isAuthenticated, isLoading } = useAppSelector(state => state.auth);

  useEffect(() => {
    // 初始化认证状态
    if (!isAuthenticated && accessToken) {
      dispatch(initializeAuth());
      // 如果有 token 但没有用户信息，尝试获取用户信息
      if (!user) {
        dispatch(getCurrentUser());
      }
    }
  }, [dispatch, isAuthenticated, accessToken, user]);

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  // 如果需要认证但用户未登录
  if (requireAuth && !isAuthenticated) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // 如果不需要认证但用户已登录（如登录页面）
  if (!requireAuth && isAuthenticated) {
    const from = location.state?.from?.pathname || '/dashboard';
    return <Navigate to={from} replace />;
  }

  // 如果需要特定角色但用户角色不匹配
  if (requiredRole && user?.role !== requiredRole) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
