# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
build/
dist/
out/
.next/
.nuxt/

# Coverage
coverage/
*.lcov

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log

# Database
*.db
*.sqlite
*.sqlite3

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo

# Temporary files
tmp/
temp/

# Package files
*.tgz
*.tar.gz

# Lock files (optional - you might want to format these)
package-lock.json
yarn.lock
pnpm-lock.yaml

# Generated files
*.min.js
*.min.css

# Documentation build
docs/_build/
docs/.vuepress/dist/

# Storybook
storybook-static/

# Docker
Dockerfile*
docker-compose*.yml

# Kubernetes
*.yaml
*.yml

# Terraform
*.tf
*.tfvars

# Generated API docs
api-docs/

# Test artifacts
test-results/
playwright-report/

# Backup files
*.bak
*.backup
*.old
