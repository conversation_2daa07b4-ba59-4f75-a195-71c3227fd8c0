import { Request, Response, NextFunction } from 'express';
import { generateRequestId } from '../utils/response';

// 请求 ID 中间件
const requestId = (_req: Request, res: Response, next: NextFunction): void => {
  // 从请求头获取或生成新的请求 ID
  const reqId = _req.get('X-Request-ID') || generateRequestId();
  
  // 设置到响应头和本地变量
  res.set('X-Request-ID', reqId);
  res.locals.requestId = reqId;
  
  next();
};

export default requestId;
