name: Release

on:
  push:
    tags:
      - 'v*'

env:
  NODE_VERSION: '18'

jobs:
  # 创建 GitHub Release
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Get version from tag
      id: get_version
      run: echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
      
    - name: Create Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ steps.get_version.outputs.VERSION }}
        draft: false
        prerelease: false
        body: |
          ## 更新内容
          
          请查看 [CHANGELOG.md](CHANGELOG.md) 了解详细更新内容。
          
          ## 安装方式
          
          ### Docker 部署
          ```bash
          docker pull promptforge/frontend:${{ steps.get_version.outputs.VERSION }}
          docker pull promptforge/backend:${{ steps.get_version.outputs.VERSION }}
          ```
          
          ### 源码部署
          ```bash
          git clone https://github.com/your-org/promptforge.git
          cd promptforge
          git checkout ${{ steps.get_version.outputs.VERSION }}
          make install
          make dev
          ```

  # 构建和发布 Docker 镜像
  build-and-push:
    name: Build and Push Docker Images
    runs-on: ubuntu-latest
    needs: create-release
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Get version from tag
      id: get_version
      run: echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
        
    - name: Login to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Build and push frontend image
      if: hashFiles('frontend/Dockerfile') != ''
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        push: true
        tags: |
          promptforge/frontend:latest
          promptforge/frontend:${{ steps.get_version.outputs.VERSION }}
          ghcr.io/${{ github.repository }}/frontend:latest
          ghcr.io/${{ github.repository }}/frontend:${{ steps.get_version.outputs.VERSION }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
    - name: Build and push backend image
      if: hashFiles('backend/Dockerfile') != ''
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        push: true
        tags: |
          promptforge/backend:latest
          promptforge/backend:${{ steps.get_version.outputs.VERSION }}
          ghcr.io/${{ github.repository }}/backend:latest
          ghcr.io/${{ github.repository }}/backend:${{ steps.get_version.outputs.VERSION }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 构建发布包
  build-artifacts:
    name: Build Release Artifacts
    runs-on: ubuntu-latest
    needs: create-release
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: |
        if [ -d "frontend" ]; then
          cd frontend && npm ci
        fi
        if [ -d "backend" ]; then
          cd backend && npm ci
        fi
        
    - name: Build frontend
      if: hashFiles('frontend/package.json') != ''
      run: cd frontend && npm run build
      
    - name: Build backend
      if: hashFiles('backend/package.json') != ''
      run: cd backend && npm run build
      
    - name: Create release archive
      run: |
        mkdir -p release
        if [ -d "frontend/build" ]; then
          cp -r frontend/build release/frontend
        fi
        if [ -d "backend/dist" ]; then
          cp -r backend/dist release/backend
        fi
        cp -r docs release/
        cp README.md LICENSE release/
        tar -czf promptforge-release.tar.gz -C release .
        
    - name: Upload release archive
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: ./promptforge-release.tar.gz
        asset_name: promptforge-release.tar.gz
        asset_content_type: application/gzip

  # 部署到生产环境
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-and-push, build-artifacts]
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Get version from tag
      id: get_version
      run: echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
      
    - name: Deploy to production
      run: |
        echo "部署版本 ${{ steps.get_version.outputs.VERSION }} 到生产环境"
        # 这里添加实际的生产环境部署脚本
        
  # 通知
  notify:
    name: Notify Release
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: always()
    
    steps:
    - name: Get version from tag
      id: get_version
      run: echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
      
    - name: Notify Slack
      if: success()
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: |
          🚀 PromptForge ${{ steps.get_version.outputs.VERSION }} 发布成功！
          
          📦 Docker 镜像已推送
          🌐 生产环境已更新
          📖 Release Notes: ${{ github.server_url }}/${{ github.repository }}/releases/tag/${{ steps.get_version.outputs.VERSION }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        
    - name: Notify on failure
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: |
          ❌ PromptForge ${{ steps.get_version.outputs.VERSION }} 发布失败！
          
          请检查 GitHub Actions 日志: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
