# 数据库配置
POSTGRES_DB=promptforge
POSTGRES_USER=promptforge
POSTGRES_PASSWORD=your-secure-password
DATABASE_URL=postgresql://promptforge:your-secure-password@localhost:5432/promptforge

# Redis 配置
REDIS_URL=redis://localhost:6379

# MinIO 配置
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=your-secure-minio-password
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=your-secure-minio-password

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
JWT_EXPIRES_IN=7d

# API 配置
PORT=3001
NODE_ENV=development

# 前端配置
REACT_APP_API_URL=http://localhost:3001/api/v1
REACT_APP_WS_URL=ws://localhost:3001

# LLM API 配置
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
GOOGLE_API_KEY=your-google-api-key

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# 监控配置
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=.txt,.md,.json,.csv

# 安全配置
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# 缓存配置
CACHE_TTL=3600  # 1 hour
CACHE_MAX_SIZE=100

# 搜索配置
ELASTICSEARCH_URL=http://localhost:9200
SEARCH_INDEX_NAME=promptforge

# 第三方登录配置
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
