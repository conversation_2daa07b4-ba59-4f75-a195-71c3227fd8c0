import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { AppState } from '@/types';

// 初始状态
const initialState: AppState = {
  theme: (localStorage.getItem('theme') as 'light' | 'dark') || 'light',
  language: (localStorage.getItem('language') as 'zh' | 'en') || 'zh',
  sidebarCollapsed: localStorage.getItem('sidebarCollapsed') === 'true',
  loading: false,
  error: null,
};

// Slice
const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
      localStorage.setItem('theme', action.payload);
    },
    toggleTheme: state => {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
      localStorage.setItem('theme', state.theme);
    },
    setLanguage: (state, action: PayloadAction<'zh' | 'en'>) => {
      state.language = action.payload;
      localStorage.setItem('language', action.payload);
    },
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload;
      localStorage.setItem('sidebarCollapsed', action.payload.toString());
    },
    toggleSidebar: state => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
      localStorage.setItem('sidebarCollapsed', state.sidebarCollapsed.toString());
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearError: state => {
      state.error = null;
    },
  },
});

export const {
  setTheme,
  toggleTheme,
  setLanguage,
  setSidebarCollapsed,
  toggleSidebar,
  setLoading,
  setError,
  clearError,
} = appSlice.actions;

export default appSlice.reducer;
