# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# Matches multiple files with brace expansion notation
# Set default charset
[*.{js,jsx,ts,tsx,json,css,scss,sass,less,html,vue,md,yml,yaml}]
indent_style = space
indent_size = 2

# Python files
[*.py]
indent_style = space
indent_size = 4

# Go files
[*.go]
indent_style = tab
indent_size = 4

# Makefile
[Makefile]
indent_style = tab

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = 80

# JSON files
[*.json]
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_size = 2

# Docker files
[Dockerfile*]
indent_size = 2

# Shell scripts
[*.{sh,bash}]
indent_size = 2

# SQL files
[*.sql]
indent_size = 2

# Configuration files
[*.{ini,cfg,conf,config}]
indent_size = 2
