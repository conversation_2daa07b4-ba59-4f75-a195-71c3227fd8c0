import { Router } from 'express';

// 路由模块
import authRoutes from './auth';
// import userRoutes from './users';
// import teamRoutes from './teams';
import promptRoutes from './prompts';
// import testRoutes from './testing';
// import analyticsRoutes from './analytics';

const router = Router();

// API 版本信息
router.get('/', (_req, res) => {
  res.json({
    success: true,
    data: {
      name: 'PromptForge API',
      version: '1.0.0',
      description: 'LLM Prompt 管理平台 API',
      documentation: '/api-docs',
      endpoints: {
        auth: '/api/v1/auth',
        users: '/api/v1/users',
        teams: '/api/v1/teams',
        prompts: '/api/v1/prompts',
        testing: '/api/v1/testing',
        analytics: '/api/v1/analytics',
      },
    },
    timestamp: new Date().toISOString(),
    requestId: res.locals.requestId,
  });
});

// 挂载路由模块
router.use('/auth', authRoutes);
// router.use('/users', userRoutes);
// router.use('/teams', teamRoutes);
router.use('/prompts', promptRoutes);
// router.use('/testing', testRoutes);
// router.use('/analytics', analyticsRoutes);

export default router;
