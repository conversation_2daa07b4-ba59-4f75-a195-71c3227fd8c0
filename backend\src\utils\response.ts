import { Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { ApiResponse, ApiError, PaginatedResponse } from '../types';

// 生成请求 ID
export const generateRequestId = (): string => {
  return uuidv4();
};

// 成功响应
export const successResponse = <T>(
  res: Response,
  data?: T,
  message?: string,
  statusCode: number = 200
): Response => {
  const response: ApiResponse<T> = {
    success: true,
    data,
    message: message || 'Success',
    timestamp: new Date().toISOString(),
    requestId: res.locals.requestId || generateRequestId(),
  };

  return res.status(statusCode).json(response);
};

// 错误响应
export const errorResponse = (
  res: Response,
  error: string | ApiError,
  statusCode: number = 500,
  details?: any
): Response => {
  const apiError: ApiError = typeof error === 'string' 
    ? { code: 'INTERNAL_ERROR', message: error, details }
    : error;

  const response: ApiResponse = {
    success: false,
    error: apiError,
    timestamp: new Date().toISOString(),
    requestId: res.locals.requestId || generateRequestId(),
  };

  return res.status(statusCode).json(response);
};

// 发送错误响应（简化版本）
export const sendErrorResponse = (
  res: Response,
  statusCode: number,
  code: string,
  message: string,
  details?: any
): Response => {
  const apiError: ApiError = { code, message, details };
  return errorResponse(res, apiError, statusCode);
};

// 分页响应
export const paginatedResponse = <T>(
  res: Response,
  items: T[],
  total: number,
  page: number,
  pageSize: number,
  message?: string
): Response => {
  const totalPages = Math.ceil(total / pageSize);
  
  const paginatedData: PaginatedResponse<T> = {
    items,
    total,
    page,
    pageSize,
    totalPages,
  };

  return successResponse(res, paginatedData, message);
};

// 创建响应
export const createdResponse = <T>(
  res: Response,
  data: T,
  message?: string
): Response => {
  return successResponse(res, data, message || 'Created successfully', 201);
};

// 无内容响应
export const noContentResponse = (res: Response): Response => {
  return res.status(204).send();
};

// 未找到响应
export const notFoundResponse = (
  res: Response,
  message?: string
): Response => {
  return errorResponse(
    res,
    {
      code: 'NOT_FOUND',
      message: message || 'Resource not found',
    },
    404
  );
};

// 未授权响应
export const unauthorizedResponse = (
  res: Response,
  message?: string
): Response => {
  return errorResponse(
    res,
    {
      code: 'UNAUTHORIZED',
      message: message || 'Unauthorized access',
    },
    401
  );
};

// 禁止访问响应
export const forbiddenResponse = (
  res: Response,
  message?: string
): Response => {
  return errorResponse(
    res,
    {
      code: 'FORBIDDEN',
      message: message || 'Access forbidden',
    },
    403
  );
};

// 验证错误响应
export const validationErrorResponse = (
  res: Response,
  errors: any[],
  message?: string
): Response => {
  return errorResponse(
    res,
    {
      code: 'VALIDATION_ERROR',
      message: message || 'Validation failed',
      details: errors,
    },
    400
  );
};

// 冲突响应
export const conflictResponse = (
  res: Response,
  message?: string
): Response => {
  return errorResponse(
    res,
    {
      code: 'CONFLICT',
      message: message || 'Resource conflict',
    },
    409
  );
};

// 限流响应
export const rateLimitResponse = (
  res: Response,
  message?: string
): Response => {
  return errorResponse(
    res,
    {
      code: 'RATE_LIMIT_EXCEEDED',
      message: message || 'Rate limit exceeded',
    },
    429
  );
};

// 服务器错误响应
export const internalErrorResponse = (
  res: Response,
  message?: string,
  details?: any
): Response => {
  return errorResponse(
    res,
    {
      code: 'INTERNAL_ERROR',
      message: message || 'Internal server error',
      details,
    },
    500
  );
};

// 服务不可用响应
export const serviceUnavailableResponse = (
  res: Response,
  message?: string
): Response => {
  return errorResponse(
    res,
    {
      code: 'SERVICE_UNAVAILABLE',
      message: message || 'Service temporarily unavailable',
    },
    503
  );
};

// 导出别名以保持向后兼容
export const sendSuccessResponse = successResponse;
