import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosRequestConfig } from 'axios';
import { ApiResponse, PaginatedResponse } from '../types';

// 创建 axios 实例
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: import.meta.env.VITE_API_URL || '/api/v1',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // 请求拦截器
  instance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      const accessToken = localStorage.getItem('accessToken');
      if (accessToken && config.headers) {
        config.headers.Authorization = `Bearer ${accessToken}`;
      }
      return config;
    },
    error => {
      return Promise.reject(error);
    },
  );

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      return response;
    },
    error => {
      if (error.response?.status === 401) {
        // Token 过期或无效，清除本地存储并跳转到登录页
        localStorage.removeItem('token');
        window.location.href = '/login';
      }
      return Promise.reject(error);
    },
  );

  return instance;
};

const api = createApiInstance();

// 通用 API 方法
const apiRequest = async <T = any>(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
  url: string,
  data?: any,
  config?: AxiosRequestConfig,
): Promise<ApiResponse<T>> => {
  const response = await api.request({
    method,
    url,
    data,
    ...config,
  });
  return response.data;
};

// 认证 API
export const authApi = {
  login: (credentials: { email: string; password: string }) =>
    apiRequest('POST', '/auth/login', credentials),

  register: (userData: { email: string; name: string; password: string }) =>
    apiRequest('POST', '/auth/register', userData),

  logout: () => apiRequest('POST', '/auth/logout'),

  getCurrentUser: () => apiRequest('GET', '/auth/me'),

  updateProfile: (userData: any) => apiRequest('PUT', '/auth/profile', userData),

  forgotPassword: (email: string) => apiRequest('POST', '/auth/forgot-password', { email }),

  resetPassword: (token: string, password: string) =>
    apiRequest('POST', '/auth/reset-password', { token, password }),

  verifyEmail: (token: string) => apiRequest('POST', '/auth/verify-email', { token }),

  refreshToken: (refreshToken: string) =>
    apiRequest('POST', '/auth/refresh', { refreshToken }),
};

// Prompt API
export const promptApi = {
  getPrompts: (params?: { page?: number; pageSize?: number; search?: string }) =>
    apiRequest<PaginatedResponse>('GET', '/prompts', undefined, { params }),

  getPromptById: (id: string) => apiRequest('GET', `/prompts/${id}`),

  createPrompt: (promptData: any) => apiRequest('POST', '/prompts', promptData),

  updatePrompt: (id: string, promptData: any) =>
    apiRequest('PUT', `/prompts/${id}`, promptData),

  deletePrompt: (id: string) => apiRequest('DELETE', `/prompts/${id}`),

  forkPrompt: (id: string) => apiRequest('POST', `/prompts/${id}/fork`),

  getPromptVersions: (promptId: string) => apiRequest('GET', `/prompts/${promptId}/versions`),

  getPromptVersion: (promptId: string, versionId: string) =>
    apiRequest('GET', `/prompts/${promptId}/versions/${versionId}`),

  createPromptVersion: (promptId: string, versionData: any) =>
    apiRequest('POST', `/prompts/${promptId}/versions`, versionData),

  restorePromptVersion: (promptId: string, versionId: string) =>
    apiRequest('POST', `/prompts/${promptId}/versions/${versionId}/restore`),

  comparePromptVersions: (promptId: string, version1: string, version2: string) =>
    apiRequest('GET', `/prompts/${promptId}/versions/compare`, undefined, {
      params: { version1, version2 },
    }),

  getTemplates: () => apiRequest('GET', '/templates'),

  createTemplate: (templateData: any) => apiRequest('POST', '/templates', templateData),

  updateTemplate: (id: string, templateData: any) =>
    apiRequest('PUT', `/templates/${id}`, templateData),

  deleteTemplate: (id: string) => apiRequest('DELETE', `/templates/${id}`),

  searchPrompts: (query: string, filters?: any) =>
    apiRequest('GET', '/prompts/search', undefined, { params: { query, ...filters } }),
};

// 团队 API
export const teamApi = {
  getTeams: () => apiRequest('GET', '/teams'),

  getTeamById: (id: string) => apiRequest('GET', `/teams/${id}`),

  createTeam: (teamData: any) => apiRequest('POST', '/teams', teamData),

  updateTeam: (id: string, teamData: any) => apiRequest('PUT', `/teams/${id}`, teamData),

  deleteTeam: (id: string) => apiRequest('DELETE', `/teams/${id}`),

  getTeamMembers: (teamId: string) => apiRequest('GET', `/teams/${teamId}/members`),

  addTeamMember: (teamId: string, memberData: { email: string; role: string }) =>
    apiRequest('POST', `/teams/${teamId}/members`, memberData),

  updateTeamMemberRole: (teamId: string, memberId: string, role: string) =>
    apiRequest('PUT', `/teams/${teamId}/members/${memberId}`, { role }),

  removeTeamMember: (teamId: string, memberId: string) =>
    apiRequest('DELETE', `/teams/${teamId}/members/${memberId}`),

  leaveTeam: (teamId: string) => apiRequest('POST', `/teams/${teamId}/leave`),

  getTeamInvitations: () => apiRequest('GET', '/teams/invitations'),

  acceptTeamInvitation: (invitationId: string) =>
    apiRequest('POST', `/teams/invitations/${invitationId}/accept`),

  rejectTeamInvitation: (invitationId: string) =>
    apiRequest('POST', `/teams/invitations/${invitationId}/reject`),
};

// 测试 API
export const testApi = {
  getTestSessions: (promptId?: string) =>
    apiRequest('GET', '/test/sessions', undefined, { params: { promptId } }),

  getTestSessionById: (id: string) => apiRequest('GET', `/test/sessions/${id}`),

  createTestSession: (sessionData: any) => apiRequest('POST', '/test/sessions', sessionData),

  deleteTestSession: (id: string) => apiRequest('DELETE', `/test/sessions/${id}`),

  runSingleTest: (testData: any) => apiRequest('POST', '/test/single', testData),

  runBatchTest: (testData: any) => apiRequest('POST', '/test/batch', testData),

  runABTest: (testData: any) => apiRequest('POST', '/test/ab', testData),

  getTestCases: (sessionId: string) => apiRequest('GET', `/test/sessions/${sessionId}/cases`),

  getTestCase: (sessionId: string, caseId: string) =>
    apiRequest('GET', `/test/sessions/${sessionId}/cases/${caseId}`),

  updateTestCase: (sessionId: string, caseId: string, caseData: any) =>
    apiRequest('PUT', `/test/sessions/${sessionId}/cases/${caseId}`, caseData),

  deleteTestCase: (sessionId: string, caseId: string) =>
    apiRequest('DELETE', `/test/sessions/${sessionId}/cases/${caseId}`),

  getTestResults: (sessionId: string) => apiRequest('GET', `/test/sessions/${sessionId}/results`),

  exportTestResults: (sessionId: string, format: 'csv' | 'json' | 'xlsx') =>
    apiRequest('GET', `/test/sessions/${sessionId}/export`, undefined, {
      params: { format },
      responseType: 'blob',
    }),
};

// 模型 API
export const modelApi = {
  getModels: () => apiRequest('GET', '/models'),

  getModelById: (id: string) => apiRequest('GET', `/models/${id}`),

  testModelConnection: (modelConfig: any) => apiRequest('POST', '/models/test', modelConfig),

  getModelUsage: (timeRange?: string) =>
    apiRequest('GET', '/models/usage', undefined, { params: { timeRange } }),

  getModelCosts: (timeRange?: string) =>
    apiRequest('GET', '/models/costs', undefined, { params: { timeRange } }),
};

// 分析 API
export const analyticsApi = {
  getPromptUsage: (promptId?: string, timeRange?: string) =>
    apiRequest('GET', '/analytics/prompts/usage', undefined, { params: { promptId, timeRange } }),

  getTeamAnalytics: (teamId: string, timeRange?: string) =>
    apiRequest('GET', `/analytics/teams/${teamId}`, undefined, { params: { timeRange } }),

  getUserAnalytics: (timeRange?: string) =>
    apiRequest('GET', '/analytics/users/me', undefined, { params: { timeRange } }),

  getSystemAnalytics: (timeRange?: string) =>
    apiRequest('GET', '/analytics/system', undefined, { params: { timeRange } }),

  exportAnalytics: (type: string, params: any) =>
    apiRequest('GET', `/analytics/${type}/export`, undefined, {
      params,
      responseType: 'blob',
    }),
};

// 文件 API
export const fileApi = {
  uploadFile: (file: File, type?: string) => {
    const formData = new FormData();
    formData.append('file', file);
    if (type) formData.append('type', type);

    return apiRequest('POST', '/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  deleteFile: (fileId: string) => apiRequest('DELETE', `/files/${fileId}`),

  getFileUrl: (fileId: string) => apiRequest('GET', `/files/${fileId}/url`),
};

export default api;
