import { Request, Response, NextFunction } from 'express';
import { Prisma } from '@prisma/client';
import { log } from '../utils/logger';
import { errorResponse, internalErrorResponse } from '../utils/response';

// 错误处理中间件
const errorHandler = (
  error: any,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  // 记录错误日志
  log.error('Error occurred:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    requestId: res.locals.requestId,
  });

  // Prisma 错误处理
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
      case 'P2002':
        // 唯一约束违反
        const field = error.meta?.target as string[] | undefined;
        errorResponse(
          res,
          {
            code: 'DUPLICATE_ENTRY',
            message: `${field?.join(', ') || 'Field'} already exists`,
            details: error.meta,
          },
          409
        );
        return;

      case 'P2025':
        // 记录未找到
        errorResponse(
          res,
          {
            code: 'NOT_FOUND',
            message: 'Record not found',
            details: error.meta,
          },
          404
        );
        return;

      case 'P2003':
        // 外键约束违反
        errorResponse(
          res,
          {
            code: 'FOREIGN_KEY_CONSTRAINT',
            message: 'Foreign key constraint failed',
            details: error.meta,
          },
          400
        );
        return;

      case 'P2014':
        // 关系违反
        errorResponse(
          res,
          {
            code: 'RELATION_VIOLATION',
            message: 'The change you are trying to make would violate the required relation',
            details: error.meta,
          },
          400
        );
        return;

      default:
        errorResponse(
          res,
          {
            code: 'DATABASE_ERROR',
            message: 'Database operation failed',
            details: process.env.NODE_ENV === 'development' ? error.meta : undefined,
          },
          500
        );
        return;
    }
  }

  // Prisma 验证错误
  if (error instanceof Prisma.PrismaClientValidationError) {
    errorResponse(
      res,
      {
        code: 'VALIDATION_ERROR',
        message: 'Invalid data provided',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined,
      },
      400
    );
    return;
  }

  // JWT 错误处理
  if (error.name === 'JsonWebTokenError') {
    errorResponse(
      res,
      {
        code: 'INVALID_TOKEN',
        message: 'Invalid authentication token',
      },
      401
    );
    return;
  }

  if (error.name === 'TokenExpiredError') {
    errorResponse(
      res,
      {
        code: 'TOKEN_EXPIRED',
        message: 'Authentication token has expired',
      },
      401
    );
    return;
  }

  // 验证错误
  if (error.name === 'ValidationError') {
    errorResponse(
      res,
      {
        code: 'VALIDATION_ERROR',
        message: error.message,
        details: error.details,
      },
      400
    );
    return;
  }

  // 文件上传错误
  if (error.code === 'LIMIT_FILE_SIZE') {
    errorResponse(
      res,
      {
        code: 'FILE_TOO_LARGE',
        message: 'File size exceeds the maximum allowed limit',
      },
      413
    );
    return;
  }

  if (error.code === 'LIMIT_FILE_COUNT') {
    errorResponse(
      res,
      {
        code: 'TOO_MANY_FILES',
        message: 'Too many files uploaded',
      },
      400
    );
    return;
  }

  // 自定义应用错误
  if (error.isOperational) {
    errorResponse(
      res,
      {
        code: error.code || 'APPLICATION_ERROR',
        message: error.message,
        details: error.details,
      },
      error.statusCode || 400
    );
    return;
  }

  // 默认内部服务器错误
  internalErrorResponse(
    res,
    'An unexpected error occurred',
    process.env.NODE_ENV === 'development' ? {
      message: error.message,
      stack: error.stack,
    } : undefined
  );
};

export default errorHandler;
