# 开发环境 Dockerfile
FROM node:18-alpine

# 安装 dumb-init
RUN apk add --no-cache dumb-init

# 设置工作目录
WORKDIR /app

# 复制 package 文件
COPY package*.json ./
COPY tsconfig.json ./
COPY nodemon.json ./

# 安装依赖
RUN npm install

# 复制源代码
COPY src/ ./src/
COPY prisma/ ./prisma/

# 生成 Prisma 客户端
RUN npx prisma generate

# 创建日志目录
RUN mkdir -p logs

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 启动开发服务器
ENTRYPOINT ["dumb-init", "--"]
CMD ["npm", "run", "dev"]
