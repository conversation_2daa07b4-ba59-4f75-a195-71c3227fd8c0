// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id           String    @id @default(cuid())
  email        String    @unique
  username     String    @unique
  passwordHash String?   @map("password_hash")
  firstName    String?   @map("first_name")
  lastName     String?   @map("last_name")
  avatarUrl    String?   @map("avatar_url")
  isActive     Boolean   @default(true) @map("is_active")
  isVerified   <PERSON>olean   @default(false) @map("is_verified")
  lastLoginAt  DateTime? @map("last_login_at")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  // 关系
  ownedTeams     Team[]         @relation("TeamOwner")
  teamMembers    TeamMember[]
  prompts        Prompt[]       @relation("PromptOwner")
  promptVersions PromptVersion[] @relation("VersionAuthor")
  testSessions   TestSession[]
  auditLogs      AuditLog[]
  userSessions   UserSession[]

  @@map("users")
}

// 团队表
model Team {
  id          String   @id @default(cuid())
  name        String
  description String?
  avatarUrl   String?  @map("avatar_url")
  ownerId     String   @map("owner_id")
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关系
  owner   User         @relation("TeamOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  members TeamMember[]
  prompts Prompt[]     @relation("TeamPrompts")

  @@map("teams")
}

// 团队成员表
model TeamMember {
  id       String   @id @default(cuid())
  teamId   String   @map("team_id")
  userId   String   @map("user_id")
  role     TeamRole @default(MEMBER)
  joinedAt DateTime @default(now()) @map("joined_at")

  // 关系
  team Team @relation(fields: [teamId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([teamId, userId])
  @@map("team_members")
}

// 团队角色枚举
enum TeamRole {
  OWNER
  ADMIN
  MEMBER
  VIEWER

  @@map("team_role")
}

// Prompt 表
model Prompt {
  id           String  @id @default(cuid())
  name         String
  description  String?
  content      String
  tags         String[]
  category     String?
  language     String  @default("en")
  modelConfig  Json?   @map("model_config")
  ownerId      String  @map("owner_id")
  teamId       String? @map("team_id")
  isPublic     Boolean @default(false) @map("is_public")
  isTemplate   Boolean @default(false) @map("is_template")
  forkFromId   String? @map("fork_from_id")
  versionCount Int     @default(1) @map("version_count")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // 关系
  owner        User            @relation("PromptOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  team         Team?           @relation("TeamPrompts", fields: [teamId], references: [id], onDelete: SetNull)
  forkFrom     Prompt?         @relation("PromptFork", fields: [forkFromId], references: [id])
  forks        Prompt[]        @relation("PromptFork")
  versions     PromptVersion[]
  testSessions TestSession[]

  @@map("prompts")
}

// Prompt 版本表
model PromptVersion {
  id              String   @id @default(cuid())
  promptId        String   @map("prompt_id")
  versionNumber   Int      @map("version_number")
  content         String
  changeMessage   String?  @map("change_message")
  authorId        String   @map("author_id")
  parentVersionId String?  @map("parent_version_id")
  isCurrent       Boolean  @default(false) @map("is_current")
  createdAt       DateTime @default(now()) @map("created_at")

  // 关系
  prompt        Prompt         @relation(fields: [promptId], references: [id], onDelete: Cascade)
  author        User           @relation("VersionAuthor", fields: [authorId], references: [id], onDelete: SetNull)
  parentVersion PromptVersion? @relation("VersionParent", fields: [parentVersionId], references: [id])
  childVersions PromptVersion[] @relation("VersionParent")
  testSessions  TestSession[]

  @@unique([promptId, versionNumber])
  @@map("prompt_versions")
}

// 测试会话表
model TestSession {
  id               String     @id @default(cuid())
  promptId         String     @map("prompt_id")
  promptVersionId  String?    @map("prompt_version_id")
  name             String?
  modelConfig      Json       @map("model_config")
  status           TestStatus @default(PENDING)
  createdBy        String     @map("created_by")
  createdAt        DateTime   @default(now()) @map("created_at")
  completedAt      DateTime?  @map("completed_at")

  // 关系
  prompt        Prompt         @relation(fields: [promptId], references: [id], onDelete: Cascade)
  promptVersion PromptVersion? @relation(fields: [promptVersionId], references: [id], onDelete: SetNull)
  creator       User           @relation(fields: [createdBy], references: [id], onDelete: SetNull)
  testCases     TestCase[]

  @@map("test_sessions")
}

// 测试用例表
model TestCase {
  id             String     @id @default(cuid())
  sessionId      String     @map("session_id")
  inputData      Json       @map("input_data")
  expectedOutput String?    @map("expected_output")
  actualOutput   String?    @map("actual_output")
  executionTime  Int?       @map("execution_time") // 毫秒
  tokensUsed     Int?       @map("tokens_used")
  cost           Decimal?   @db.Decimal(10, 6)
  status         TestStatus @default(PENDING)
  errorMessage   String?    @map("error_message")
  createdAt      DateTime   @default(now()) @map("created_at")
  completedAt    DateTime?  @map("completed_at")

  // 关系
  session TestSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@map("test_cases")
}

// 测试状态枚举
enum TestStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED

  @@map("test_status")
}

// 用户会话表 (JWT 黑名单)
model UserSession {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  tokenJti  String   @unique @map("token_jti")
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")

  // 关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

// 审计日志表
model AuditLog {
  id           String   @id @default(cuid())
  userId       String?  @map("user_id")
  action       String
  resourceType String   @map("resource_type")
  resourceId   String?  @map("resource_id")
  oldValues    Json?    @map("old_values")
  newValues    Json?    @map("new_values")
  ipAddress    String?  @map("ip_address")
  userAgent    String?  @map("user_agent")
  createdAt    DateTime @default(now()) @map("created_at")

  // 关系
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("audit_logs")
}
