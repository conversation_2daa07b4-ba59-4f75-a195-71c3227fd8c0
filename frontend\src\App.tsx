
import { RouterProvider } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { store } from './store';
import { router } from './router';
import AuthInitializer from './components/auth/AuthInitializer';
import './App.css';

function App() {
  return (
    <Provider store={store}>
      <ConfigProvider locale={zhCN}>
        <AuthInitializer>
          <RouterProvider router={router} />
        </AuthInitializer>
      </ConfigProvider>
    </Provider>
  );
}

export default App;
