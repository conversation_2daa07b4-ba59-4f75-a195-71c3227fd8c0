import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Tag,
  Avatar,
  Tooltip,
  Dropdown,
  Modal,
  message,
  Row,
  Col,
  Typography,
  Divider,
  Descriptions,
  Spin,
  Alert,
  Timeline,
  Badge,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  PlayCircleOutlined,
  MoreOutlined,
  UserOutlined,
  CalendarOutlined,
  TagOutlined,
  GlobalOutlined,
  LockOutlined,
  HistoryOutlined,
  SettingOutlined,
  EyeOutlined,
  ShareAltOutlined,
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { promptApi } from '../../services/api';
import { Prompt, PromptVersion } from '../../types';
import { formatDistanceToNow, isValid, parseISO } from 'date-fns';
import { zhCN } from 'date-fns/locale';

const { Text, Title, Paragraph } = Typography;

// 安全的日期格式化函数
const formatDate = (dateString: string) => {
  try {
    const date = parseISO(dateString);
    if (!isValid(date)) {
      return '无效日期';
    }
    return formatDistanceToNow(date, { addSuffix: true, locale: zhCN });
  } catch (error) {
    return '无效日期';
  }
};

const PromptDetail: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [prompt, setPrompt] = useState<Prompt | null>(null);
  const [versions, setVersions] = useState<PromptVersion[]>([]);
  const [loading, setLoading] = useState(true);
  const [versionsLoading, setVersionsLoading] = useState(false);
  const [showVersions, setShowVersions] = useState(false);

  // 获取 Prompt 详情
  const fetchPrompt = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const response = await promptApi.getPromptById(id);
      setPrompt(response.data);
    } catch (error) {
      console.error('获取 Prompt 详情失败:', error);
      message.error('获取 Prompt 详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取版本历史
  const fetchVersions = async () => {
    if (!id) return;

    setVersionsLoading(true);
    try {
      const response = await promptApi.getPromptVersions(id);
      setVersions(response.data);
    } catch (error) {
      console.error('获取版本历史失败:', error);
      message.error('获取版本历史失败');
    } finally {
      setVersionsLoading(false);
    }
  };

  useEffect(() => {
    fetchPrompt();
  }, [id]);

  useEffect(() => {
    if (showVersions && versions.length === 0) {
      fetchVersions();
    }
  }, [showVersions]);

  // 删除 Prompt
  const handleDelete = async () => {
    if (!prompt) return;

    try {
      await promptApi.deletePrompt(prompt.id);
      message.success('删除成功');
      navigate('/prompts');
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 复制 Prompt
  const handleFork = async () => {
    if (!prompt) return;

    try {
      await promptApi.forkPrompt(prompt.id);
      message.success('复制成功');
      navigate('/prompts');
    } catch (error) {
      console.error('复制失败:', error);
      message.error('复制失败');
    }
  };

  // 操作菜单
  const actionMenu = {
    items: [
      {
        key: 'test',
        icon: <PlayCircleOutlined />,
        label: '测试运行',
        onClick: () => navigate(`/prompts/${id}/test`),
      },
      {
        key: 'share',
        icon: <ShareAltOutlined />,
        label: '分享',
        onClick: () => {
          // TODO: 实现分享功能
          message.info('分享功能开发中');
        },
      },
      {
        type: 'divider',
      },
      {
        key: 'fork',
        icon: <CopyOutlined />,
        label: '复制',
        onClick: handleFork,
      },
      {
        key: 'delete',
        icon: <DeleteOutlined />,
        label: '删除',
        danger: true,
        onClick: () => {
          Modal.confirm({
            title: '确认删除',
            content: `确定要删除 Prompt "${prompt?.name}" 吗？此操作不可恢复。`,
            okText: '删除',
            okType: 'danger',
            cancelText: '取消',
            onOk: handleDelete,
          });
        },
      },
    ],
  };

  if (loading) {
    return (
      <div style={{ padding: 24, textAlign: 'center' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  if (!prompt) {
    return (
      <div style={{ padding: 24 }}>
        <Alert
          message="Prompt 不存在"
          description="您访问的 Prompt 不存在或已被删除。"
          type="error"
          showIcon
          action={
            <Button size="small" onClick={() => navigate('/prompts')}>
              返回列表
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div style={{ padding: 24 }}>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        <Space style={{ marginBottom: 16 }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/prompts')}
          >
            返回列表
          </Button>
        </Space>

        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div>
            <Title level={2} style={{ margin: 0, marginBottom: 8 }}>
              {prompt.name}
            </Title>
            {prompt.description && (
              <Paragraph type="secondary" style={{ fontSize: 16, margin: 0 }}>
                {prompt.description}
              </Paragraph>
            )}
          </div>

          <Space>
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => navigate(`/prompts/${prompt.id}/edit`)}
            >
              编辑
            </Button>
            <Dropdown menu={actionMenu} trigger={['click']}>
              <Button icon={<MoreOutlined />} />
            </Dropdown>
          </Space>
        </div>
      </div>

      <Row gutter={24}>
        {/* 左侧主要内容 */}
        <Col span={16}>
          {/* Prompt 内容 */}
          <Card title="Prompt 内容" style={{ marginBottom: 24 }}>
            <div
              style={{
                background: '#f5f5f5',
                padding: 16,
                borderRadius: 6,
                fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                fontSize: 14,
                lineHeight: 1.6,
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
              }}
            >
              {prompt.content}
            </div>
          </Card>

          {/* 模型配置 */}
          {prompt.modelConfig && (
            <Card
              title={
                <Space>
                  <SettingOutlined />
                  模型配置
                </Space>
              }
              style={{ marginBottom: 24 }}
            >
              <Descriptions column={2} size="small">
                <Descriptions.Item label="提供商">
                  {prompt.modelConfig.provider || '未设置'}
                </Descriptions.Item>
                <Descriptions.Item label="模型">
                  {prompt.modelConfig.model || '未设置'}
                </Descriptions.Item>
                <Descriptions.Item label="Temperature">
                  {prompt.modelConfig.temperature ?? '未设置'}
                </Descriptions.Item>
                <Descriptions.Item label="Max Tokens">
                  {prompt.modelConfig.maxTokens ?? '未设置'}
                </Descriptions.Item>
                <Descriptions.Item label="Top P">
                  {prompt.modelConfig.topP ?? '未设置'}
                </Descriptions.Item>
                <Descriptions.Item label="Frequency Penalty">
                  {prompt.modelConfig.frequencyPenalty ?? '未设置'}
                </Descriptions.Item>
                <Descriptions.Item label="Presence Penalty">
                  {prompt.modelConfig.presencePenalty ?? '未设置'}
                </Descriptions.Item>
                <Descriptions.Item label="停止序列">
                  {prompt.modelConfig.stopSequences?.length
                    ? prompt.modelConfig.stopSequences.join(', ')
                    : '未设置'}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          )}

          {/* 版本历史 */}
          <Card
            title={
              <Space>
                <HistoryOutlined />
                版本历史
                <Badge count={versions.length} />
              </Space>
            }
            extra={
              <Button
                type="link"
                onClick={() => setShowVersions(!showVersions)}
              >
                {showVersions ? '收起' : '展开'}
              </Button>
            }
          >
            {showVersions && (
              <Spin spinning={versionsLoading}>
                {versions.length > 0 ? (
                  <Timeline
                    items={versions.map((version, index) => ({
                      color: version.isCurrent ? 'green' : 'blue',
                      children: (
                        <div>
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Space>
                              <Text strong>v{version.versionNumber}</Text>
                              {version.isCurrent && <Badge status="success" text="当前版本" />}
                            </Space>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              {formatDate(version.createdAt)}
                            </Text>
                          </div>
                          {version.changeMessage && (
                            <Text type="secondary">{version.changeMessage}</Text>
                          )}
                          <div style={{ marginTop: 8 }}>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              by {version.author?.name || '未知'}
                            </Text>
                          </div>
                        </div>
                      ),
                    }))}
                  />
                ) : (
                  <Text type="secondary">暂无版本历史</Text>
                )}
              </Spin>
            )}
          </Card>
        </Col>

        {/* 右侧信息面板 */}
        <Col span={8}>
          {/* 基本信息 */}
          <Card title="基本信息" style={{ marginBottom: 24 }}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="创建者">
                <Space>
                  <Avatar size="small" icon={<UserOutlined />} />
                  {prompt.owner?.name || '未知'}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                <Tooltip title={new Date(prompt.createdAt).toLocaleString()}>
                  {formatDate(prompt.createdAt)}
                </Tooltip>
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                <Tooltip title={new Date(prompt.updatedAt).toLocaleString()}>
                  {formatDate(prompt.updatedAt)}
                </Tooltip>
              </Descriptions.Item>
              <Descriptions.Item label="分类">
                {prompt.category ? (
                  <Tag color="blue">{prompt.category}</Tag>
                ) : (
                  <Text type="secondary">未分类</Text>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="语言">
                {prompt.language ? (
                  <Tag color="green">{prompt.language}</Tag>
                ) : (
                  <Text type="secondary">未设置</Text>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Space>
                  {prompt.isPublic ? (
                    <Tag icon={<GlobalOutlined />} color="success">
                      公开
                    </Tag>
                  ) : (
                    <Tag icon={<LockOutlined />} color="warning">
                      私有
                    </Tag>
                  )}
                  {prompt.isTemplate && (
                    <Tag icon={<TagOutlined />} color="processing">
                      模板
                    </Tag>
                  )}
                </Space>
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 标签 */}
          {prompt.tags && prompt.tags.length > 0 && (
            <Card title="标签" style={{ marginBottom: 24 }}>
              <div>
                {prompt.tags.map(tag => (
                  <Tag key={tag} style={{ marginBottom: 8 }}>
                    {tag}
                  </Tag>
                ))}
              </div>
            </Card>
          )}

          {/* 团队信息 */}
          {prompt.team && (
            <Card title="团队" style={{ marginBottom: 24 }}>
              <Space>
                <Avatar size="small" icon={<UserOutlined />} />
                {prompt.team.name}
              </Space>
            </Card>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default PromptDetail;
