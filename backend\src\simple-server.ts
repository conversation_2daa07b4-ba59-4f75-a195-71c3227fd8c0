import express from 'express';
import { createServer } from 'http';
import config from './config';
import { log } from './utils/logger';

// 创建 Express 应用
const app = express();

// 基础中间件
app.use(express.json());

// 简单路由
app.get('/', (_req, res) => {
  res.json({ message: 'PromptForge Backend is running!' });
});

app.get('/health', (_req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 创建 HTTP 服务器
const server = createServer(app);

// 启动服务器
const startServer = async (): Promise<void> => {
  try {
    server.listen(config.server.port, () => {
      log.info('🚀 Simple server started successfully');
      log.info(`🌐 Server running at http://localhost:${config.server.port}`);
    });
  } catch (error) {
    log.error('Failed to start server:', error);
    process.exit(1);
  }
};

startServer();
