import { Router, Request, Response } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { authenticate } from '../middleware/auth';
import { sendSuccessResponse, sendErrorResponse } from '../utils/response';
// import prisma from '../config/database';

const router = Router();

// 验证中间件
const validatePromptCreation = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Name must be between 1 and 255 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description must not exceed 1000 characters'),
  body('content')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Content is required'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('category')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Category must not exceed 100 characters'),
  body('language')
    .optional()
    .trim()
    .isLength({ min: 2, max: 10 })
    .withMessage('Language must be between 2 and 10 characters'),
  body('isPublic')
    .optional()
    .isBoolean()
    .withMessage('isPublic must be a boolean'),
  body('isTemplate')
    .optional()
    .isBoolean()
    .withMessage('isTemplate must be a boolean'),
];

const validatePromptUpdate = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Name must be between 1 and 255 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description must not exceed 1000 characters'),
  body('content')
    .optional()
    .trim()
    .isLength({ min: 1 })
    .withMessage('Content cannot be empty'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('category')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Category must not exceed 100 characters'),
  body('language')
    .optional()
    .trim()
    .isLength({ min: 2, max: 10 })
    .withMessage('Language must be between 2 and 10 characters'),
  body('isPublic')
    .optional()
    .isBoolean()
    .withMessage('isPublic must be a boolean'),
];

const validatePromptId = [
  param('id')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Prompt ID is required'),
];

const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('pageSize')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Page size must be between 1 and 100'),
  query('search')
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage('Search term must not exceed 255 characters'),
];

// 错误处理中间件
const handleValidationErrors = (req: Request, res: Response, next: any): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    sendErrorResponse(res, 400, 'VALIDATION_ERROR', 'Invalid input data', {
      errors: errors.array(),
    });
    return;
  }
  next();
};

/**
 * @swagger
 * /api/v1/prompts:
 *   get:
 *     summary: Get prompts list
 *     tags: [Prompts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for name or description
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by category
 *       - in: query
 *         name: isPublic
 *         schema:
 *           type: boolean
 *         description: Filter by public status
 *     responses:
 *       200:
 *         description: Prompts retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get(
  '/',
  authenticate,
  validatePagination,
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.userId;
      const page = parseInt(req.query.page as string) || 1;
      const pageSize = parseInt(req.query.pageSize as string) || 20;
      const search = req.query.search as string;
      const category = req.query.category as string;
      const isPublic = req.query.isPublic === 'true';

      // 暂时使用模拟数据
      const mockPrompts = [
        {
          id: 'prompt_1',
          name: 'Code Review Assistant',
          description: 'A prompt for reviewing code and providing feedback',
          content: 'Please review the following code and provide constructive feedback...',
          tags: ['code-review', 'development'],
          category: 'Development',
          language: 'en',
          modelConfig: {
            provider: 'openai',
            model: 'gpt-4',
            temperature: 0.3,
            maxTokens: 1000,
          },
          ownerId: userId,
          teamId: null,
          isPublic: false,
          isTemplate: false,
          forkFromId: null,
          versionCount: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: 'prompt_2',
          name: 'Email Writer',
          description: 'Generate professional emails',
          content: 'Write a professional email with the following requirements...',
          tags: ['email', 'communication'],
          category: 'Business',
          language: 'en',
          modelConfig: {
            provider: 'openai',
            model: 'gpt-3.5-turbo',
            temperature: 0.7,
            maxTokens: 500,
          },
          ownerId: userId,
          teamId: null,
          isPublic: true,
          isTemplate: true,
          forkFromId: null,
          versionCount: 2,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      // 应用搜索过滤
      let filteredPrompts = mockPrompts;
      if (search) {
        filteredPrompts = filteredPrompts.filter(
          prompt =>
            prompt.name.toLowerCase().includes(search.toLowerCase()) ||
            prompt.description?.toLowerCase().includes(search.toLowerCase())
        );
      }
      if (category) {
        filteredPrompts = filteredPrompts.filter(prompt => prompt.category === category);
      }
      if (req.query.isPublic !== undefined) {
        filteredPrompts = filteredPrompts.filter(prompt => prompt.isPublic === isPublic);
      }

      // 分页
      const total = filteredPrompts.length;
      const totalPages = Math.ceil(total / pageSize);
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedPrompts = filteredPrompts.slice(startIndex, endIndex);

      return sendSuccessResponse(res, {
        prompts: paginatedPrompts,
        pagination: {
          total,
          page,
          pageSize,
          totalPages,
        },
      }, 'Prompts retrieved successfully', 200);

    } catch (error) {
      console.error('Error fetching prompts:', error);
      return sendErrorResponse(res, 500, 'INTERNAL_ERROR', 'Failed to fetch prompts');
    }
  }
);

/**
 * @swagger
 * /api/v1/prompts/{id}:
 *   get:
 *     summary: Get prompt by ID
 *     tags: [Prompts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Prompt ID
 *     responses:
 *       200:
 *         description: Prompt retrieved successfully
 *       404:
 *         description: Prompt not found
 *       401:
 *         description: Unauthorized
 */
router.get(
  '/:id',
  authenticate,
  validatePromptId,
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const userId = req.user!.userId;

      // 暂时使用模拟数据
      const mockPrompt = {
        id,
        name: 'Code Review Assistant',
        description: 'A prompt for reviewing code and providing feedback',
        content: 'Please review the following code and provide constructive feedback:\n\n{code}\n\nFocus on:\n- Code quality and best practices\n- Potential bugs or issues\n- Performance improvements\n- Readability and maintainability',
        tags: ['code-review', 'development'],
        category: 'Development',
        language: 'en',
        modelConfig: {
          provider: 'openai',
          model: 'gpt-4',
          temperature: 0.3,
          maxTokens: 1000,
        },
        ownerId: userId,
        teamId: null,
        isPublic: false,
        isTemplate: false,
        forkFromId: null,
        versionCount: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return sendSuccessResponse(res, { prompt: mockPrompt }, 'Prompt retrieved successfully', 200);

    } catch (error) {
      console.error('Error fetching prompt:', error);
      return sendErrorResponse(res, 500, 'INTERNAL_ERROR', 'Failed to fetch prompt');
    }
  }
);

/**
 * @swagger
 * /api/v1/prompts:
 *   post:
 *     summary: Create a new prompt
 *     tags: [Prompts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - content
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 255
 *               description:
 *                 type: string
 *                 maxLength: 1000
 *               content:
 *                 type: string
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               category:
 *                 type: string
 *                 maxLength: 100
 *               language:
 *                 type: string
 *                 default: en
 *               modelConfig:
 *                 type: object
 *               isPublic:
 *                 type: boolean
 *                 default: false
 *               isTemplate:
 *                 type: boolean
 *                 default: false
 *     responses:
 *       201:
 *         description: Prompt created successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 */
router.post(
  '/',
  authenticate,
  validatePromptCreation,
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const userId = req.user!.userId;
      const {
        name,
        description,
        content,
        tags = [],
        category,
        language = 'en',
        modelConfig,
        teamId,
        isPublic = false,
        isTemplate = false,
      } = req.body;

      // 生成新的 Prompt ID
      const promptId = `prompt_${Date.now()}`;

      // 暂时使用模拟数据创建
      const newPrompt = {
        id: promptId,
        name,
        description,
        content,
        tags,
        category,
        language,
        modelConfig,
        ownerId: userId,
        teamId: teamId || null,
        isPublic,
        isTemplate,
        forkFromId: null,
        versionCount: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // 同时创建第一个版本
      const firstVersion = {
        id: `version_${Date.now()}`,
        promptId,
        versionNumber: 1,
        content,
        changeMessage: 'Initial version',
        authorId: userId,
        parentVersionId: null,
        isCurrent: true,
        createdAt: new Date().toISOString(),
      };

      return sendSuccessResponse(
        res,
        {
          prompt: newPrompt,
          version: firstVersion,
        },
        'Prompt created successfully',
        201
      );

    } catch (error) {
      console.error('Error creating prompt:', error);
      return sendErrorResponse(res, 500, 'INTERNAL_ERROR', 'Failed to create prompt');
    }
  }
);

/**
 * @swagger
 * /api/v1/prompts/{id}:
 *   put:
 *     summary: Update a prompt
 *     tags: [Prompts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Prompt ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 255
 *               description:
 *                 type: string
 *                 maxLength: 1000
 *               content:
 *                 type: string
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               category:
 *                 type: string
 *                 maxLength: 100
 *               language:
 *                 type: string
 *               modelConfig:
 *                 type: object
 *               isPublic:
 *                 type: boolean
 *               changeMessage:
 *                 type: string
 *                 description: Message describing the changes (for version control)
 *     responses:
 *       200:
 *         description: Prompt updated successfully
 *       404:
 *         description: Prompt not found
 *       403:
 *         description: Not authorized to update this prompt
 *       401:
 *         description: Unauthorized
 */
router.put(
  '/:id',
  authenticate,
  validatePromptId,
  validatePromptUpdate,
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const userId = req.user!.userId;
      const {
        name,
        description,
        content,
        tags,
        category,
        language,
        modelConfig,
        isPublic,
        changeMessage = 'Updated prompt',
      } = req.body;

      // 暂时使用模拟数据检查权限
      // 在真实实现中，这里会查询数据库检查用户是否有权限修改此 Prompt

      // 模拟更新的 Prompt
      const updatedPrompt = {
        id,
        name: name || 'Code Review Assistant',
        description: description || 'A prompt for reviewing code and providing feedback',
        content: content || 'Please review the following code...',
        tags: tags || ['code-review', 'development'],
        category: category || 'Development',
        language: language || 'en',
        modelConfig: modelConfig || {
          provider: 'openai',
          model: 'gpt-4',
          temperature: 0.3,
          maxTokens: 1000,
        },
        ownerId: userId,
        teamId: null,
        isPublic: isPublic !== undefined ? isPublic : false,
        isTemplate: false,
        forkFromId: null,
        versionCount: 2, // 增加版本计数
        createdAt: new Date(Date.now() - 86400000).toISOString(), // 昨天创建
        updatedAt: new Date().toISOString(), // 现在更新
      };

      // 如果内容发生变化，创建新版本
      let newVersion = null;
      if (content) {
        newVersion = {
          id: `version_${Date.now()}`,
          promptId: id,
          versionNumber: 2,
          content,
          changeMessage,
          authorId: userId,
          parentVersionId: `version_${Date.now() - 1000}`, // 模拟父版本ID
          isCurrent: true,
          createdAt: new Date().toISOString(),
        };
      }

      return sendSuccessResponse(
        res,
        {
          prompt: updatedPrompt,
          ...(newVersion && { newVersion }),
        },
        'Prompt updated successfully',
        200
      );

    } catch (error) {
      console.error('Error updating prompt:', error);
      return sendErrorResponse(res, 500, 'INTERNAL_ERROR', 'Failed to update prompt');
    }
  }
);

/**
 * @swagger
 * /api/v1/prompts/{id}:
 *   delete:
 *     summary: Delete a prompt
 *     tags: [Prompts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Prompt ID
 *     responses:
 *       200:
 *         description: Prompt deleted successfully
 *       404:
 *         description: Prompt not found
 *       403:
 *         description: Not authorized to delete this prompt
 *       401:
 *         description: Unauthorized
 */
router.delete(
  '/:id',
  authenticate,
  validatePromptId,
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const userId = req.user!.userId;

      // 暂时使用模拟数据检查权限
      // 在真实实现中，这里会查询数据库检查用户是否有权限删除此 Prompt
      // 模拟权限检查：确保只有 Prompt 所有者可以删除
      console.log(`User ${userId} attempting to delete prompt ${id}`);

      // 模拟删除操作
      // await prisma.prompt.delete({
      //   where: { id, ownerId: userId }
      // });

      return sendSuccessResponse(
        res,
        { deletedPromptId: id },
        'Prompt deleted successfully',
        200
      );

    } catch (error) {
      console.error('Error deleting prompt:', error);
      return sendErrorResponse(res, 500, 'INTERNAL_ERROR', 'Failed to delete prompt');
    }
  }
);

/**
 * @swagger
 * /api/v1/prompts/{id}/versions:
 *   get:
 *     summary: Get prompt versions
 *     tags: [Prompts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Prompt ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *         description: Number of versions per page
 *     responses:
 *       200:
 *         description: Prompt versions retrieved successfully
 *       404:
 *         description: Prompt not found
 *       401:
 *         description: Unauthorized
 */
router.get(
  '/:id/versions',
  authenticate,
  validatePromptId,
  validatePagination,
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const page = parseInt(req.query.page as string) || 1;
      const pageSize = parseInt(req.query.pageSize as string) || 20;

      // 暂时使用模拟数据
      const mockVersions = [
        {
          id: 'version_2',
          promptId: id,
          versionNumber: 2,
          content: 'Please review the following code and provide detailed feedback:\n\n{code}\n\nFocus on:\n- Code quality and best practices\n- Potential bugs or security issues\n- Performance improvements\n- Readability and maintainability\n- Suggestions for refactoring',
          changeMessage: 'Added more detailed review criteria',
          authorId: req.user!.userId,
          parentVersionId: 'version_1',
          isCurrent: true,
          createdAt: new Date().toISOString(),
        },
        {
          id: 'version_1',
          promptId: id,
          versionNumber: 1,
          content: 'Please review the following code and provide constructive feedback:\n\n{code}\n\nFocus on:\n- Code quality and best practices\n- Potential bugs or issues\n- Performance improvements\n- Readability and maintainability',
          changeMessage: 'Initial version',
          authorId: req.user!.userId,
          parentVersionId: null,
          isCurrent: false,
          createdAt: new Date(Date.now() - 86400000).toISOString(),
        },
      ];

      // 分页
      const total = mockVersions.length;
      const totalPages = Math.ceil(total / pageSize);
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedVersions = mockVersions.slice(startIndex, endIndex);

      return sendSuccessResponse(
        res,
        {
          versions: paginatedVersions,
          pagination: {
            total,
            page,
            pageSize,
            totalPages,
          },
        },
        'Prompt versions retrieved successfully',
        200
      );

    } catch (error) {
      console.error('Error fetching prompt versions:', error);
      return sendErrorResponse(res, 500, 'INTERNAL_ERROR', 'Failed to fetch prompt versions');
    }
  }
);

/**
 * @swagger
 * /api/v1/prompts/{id}/versions:
 *   post:
 *     summary: Create a new version of a prompt
 *     tags: [Prompts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Prompt ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - content
 *             properties:
 *               content:
 *                 type: string
 *               changeMessage:
 *                 type: string
 *                 maxLength: 500
 *     responses:
 *       201:
 *         description: Prompt version created successfully
 *       404:
 *         description: Prompt not found
 *       403:
 *         description: Not authorized to create version for this prompt
 *       401:
 *         description: Unauthorized
 */
router.post(
  '/:id/versions',
  authenticate,
  validatePromptId,
  [
    body('content')
      .trim()
      .isLength({ min: 1 })
      .withMessage('Content is required'),
    body('changeMessage')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Change message must not exceed 500 characters'),
  ],
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const userId = req.user!.userId;
      const { content, changeMessage = 'New version' } = req.body;

      // 暂时使用模拟数据
      const newVersion = {
        id: `version_${Date.now()}`,
        promptId: id,
        versionNumber: 3, // 模拟下一个版本号
        content,
        changeMessage,
        authorId: userId,
        parentVersionId: 'version_2', // 模拟当前版本ID
        isCurrent: true,
        createdAt: new Date().toISOString(),
      };

      return sendSuccessResponse(
        res,
        { version: newVersion },
        'Prompt version created successfully',
        201
      );

    } catch (error) {
      console.error('Error creating prompt version:', error);
      return sendErrorResponse(res, 500, 'INTERNAL_ERROR', 'Failed to create prompt version');
    }
  }
);

export default router;
