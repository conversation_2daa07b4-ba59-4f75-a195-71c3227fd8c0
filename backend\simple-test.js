require('dotenv').config();
const express = require('express');

const app = express();
const port = process.env.PORT || 3001;

// 基础中间件
app.use(express.json());

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'PromptForge Backend is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// 根路径
app.get('/', (req, res) => {
  res.json({
    success: true,
    data: {
      name: 'PromptForge API',
      version: '1.0.0',
      description: 'LLM Prompt 管理平台 API'
    }
  });
});

app.listen(port, () => {
  console.log(`🚀 Server started successfully`);
  console.log(`🌐 Server running at http://localhost:${port}`);
  console.log(`❤️  Health Check: http://localhost:${port}/health`);
});
