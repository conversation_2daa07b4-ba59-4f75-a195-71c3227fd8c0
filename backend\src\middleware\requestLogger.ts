import { Request, Response } from 'express';
import morgan from 'morgan';
import { log } from '../utils/logger';
import config from '../config';

// 自定义 morgan token
morgan.token('id', (_req: Request, res: Response) => res.locals.requestId);
morgan.token('user', (req: any) => req.user?.id || 'anonymous');

// 开发环境格式
const devFormat = ':method :url :status :res[content-length] - :response-time ms [:id] [:user]';

// 生产环境格式
const prodFormat = ':remote-addr - :remote-user [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" [:id] [:user]';

// 创建 morgan 中间件
const requestLogger = morgan(
  config.server.env === 'development' ? devFormat : prodFormat,
  {
    stream: {
      write: (message: string) => {
        // 移除换行符并记录到 winston
        log.info(message.trim());
      },
    },
    skip: (req: Request, _res: Response) => {
      // 跳过健康检查和静态资源请求
      return req.url === '/health' || req.url.startsWith('/static');
    },
  }
);

export default requestLogger;
