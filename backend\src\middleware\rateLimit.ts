import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import config from '../config';
import { rateLimitResponse } from '../utils/response';
import { log } from '../utils/logger';

// 基础限流配置
const createRateLimit = (options: Partial<any> = {}) => {
  return rateLimit({
    windowMs: config.rateLimit.windowMs,
    max: config.rateLimit.maxRequests,
    message: 'Too many requests from this IP, please try again later',
    standardHeaders: true,
    legacyHeaders: false,
    
    // 自定义响应处理
    handler: (req: Request, res: Response) => {
      log.warn('Rate limit exceeded', {
        ip: req.ip,
        url: req.url,
        userAgent: req.get('User-Agent'),
        requestId: res.locals.requestId,
      });
      
      rateLimitResponse(res, 'Too many requests, please try again later');
    },
    
    // 跳过成功的请求
    skipSuccessfulRequests: false,
    
    // 跳过失败的请求
    skipFailedRequests: false,
    
    // 使用默认的键生成器，它会正确处理 IPv6
    // keyGenerator: 默认使用 req.ip
    
    ...options,
  });
};

// 默认限流中间件
export const defaultRateLimit = createRateLimit();

// 严格限流（用于敏感操作）
export const strictRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15 分钟
  max: 5, // 最多 5 次请求
  message: 'Too many attempts, please try again in 15 minutes',
});

// 认证相关限流
export const authRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15 分钟
  max: 10, // 最多 10 次登录尝试
  message: 'Too many login attempts, please try again in 15 minutes',
  skipSuccessfulRequests: true, // 跳过成功的登录
});

// API 调用限流
export const apiRateLimit = createRateLimit({
  windowMs: 60 * 1000, // 1 分钟
  max: 100, // 每分钟最多 100 次 API 调用
  message: 'API rate limit exceeded, please slow down',
});

// 文件上传限流
export const uploadRateLimit = createRateLimit({
  windowMs: 60 * 60 * 1000, // 1 小时
  max: 50, // 每小时最多 50 次上传
  message: 'Upload rate limit exceeded, please try again later',
});

// 测试执行限流
export const testRateLimit = createRateLimit({
  windowMs: 60 * 1000, // 1 分钟
  max: 20, // 每分钟最多 20 次测试
  message: 'Test execution rate limit exceeded, please slow down',
});

export default defaultRateLimit;
