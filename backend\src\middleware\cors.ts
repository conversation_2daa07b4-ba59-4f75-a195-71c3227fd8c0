import cors from 'cors';
import config from '../config';

// CORS 配置
const corsOptions: cors.CorsOptions = {
  origin: (origin, callback) => {
    // 允许的源
    const allowedOrigins = Array.isArray(config.cors.origin) 
      ? config.cors.origin 
      : [config.cors.origin];

    // 开发环境允许所有源
    if (config.server.env === 'development') {
      return callback(null, true);
    }

    // 检查源是否在允许列表中
    if (!origin || allowedOrigins.includes(origin)) {
      return callback(null, true);
    }

    // 拒绝请求
    callback(new Error('Not allowed by CORS'));
  },
  
  // 允许的 HTTP 方法
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  
  // 允许的请求头
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-Request-ID',
    'X-API-Key',
  ],
  
  // 暴露的响应头
  exposedHeaders: [
    'X-Request-ID',
    'X-Total-Count',
    'X-Page-Count',
  ],
  
  // 允许携带凭证
  credentials: true,
  
  // 预检请求缓存时间（秒）
  maxAge: 86400, // 24 小时
  
  // 预检请求成功状态码
  optionsSuccessStatus: 200,
};

export default cors(corsOptions);
