version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: promptforge-postgres
    environment:
      POSTGRES_DB: promptforge
      POSTGRES_USER: promptforge
      POSTGRES_PASSWORD: promptforge123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - promptforge-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: promptforge-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - promptforge-network

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: promptforge-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - promptforge-network

  # Elasticsearch (可选，用于全文搜索)
  elasticsearch:
    image: elasticsearch:8.11.0
    container_name: promptforge-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - promptforge-network

  # 后端服务 (开发模式)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: promptforge-backend
    environment:
      NODE_ENV: development
      DATABASE_URL: *****************************************************/promptforge
      REDIS_URL: redis://redis:6379
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin123
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      PORT: 3001
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
      - minio
    networks:
      - promptforge-network
    command: npm run dev

  # 前端服务 (开发模式)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: promptforge-frontend
    environment:
      REACT_APP_API_URL: http://localhost:3001/api/v1
      REACT_APP_WS_URL: ws://localhost:3001
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - promptforge-network
    command: npm start

volumes:
  postgres_data:
  redis_data:
  minio_data:
  elasticsearch_data:

networks:
  promptforge-network:
    driver: bridge
