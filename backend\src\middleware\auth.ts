import { Request, Response, NextFunction } from 'express';
import { verifyAccessToken, extractBearerToken, JwtPayload } from '../utils/auth';
import { sendErrorResponse } from '../utils/response';

// 扩展 Request 接口以包含用户信息
declare global {
  namespace Express {
    interface Request {
      user?: JwtPayload;
    }
  }
}

// 认证中间件
export const authenticate = (req: Request, res: Response, next: NextFunction): void => {
  try {
    const authHeader = req.headers.authorization;
    const token = extractBearerToken(authHeader);
    
    if (!token) {
      sendErrorResponse(res, 401, 'UNAUTHORIZED', 'Access token is required');
      return;
    }
    
    const payload = verifyAccessToken(token);
    req.user = payload;
    next();
  } catch (error) {
    sendErrorResponse(res, 401, 'UNAUTHORIZED', 'Invalid or expired access token');
  }
};

// 可选认证中间件（不强制要求认证）
export const optionalAuthenticate = (req: Request, _res: Response, next: NextFunction): void => {
  try {
    const authHeader = req.headers.authorization;
    const token = extractBearerToken(authHeader);
    
    if (token) {
      const payload = verifyAccessToken(token);
      req.user = payload;
    }
    
    next();
  } catch (error) {
    // 忽略认证错误，继续处理请求
    next();
  }
};

// 角色权限检查中间件
export const requireRole = (...roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      sendErrorResponse(res, 401, 'UNAUTHORIZED', 'Authentication required');
      return;
    }
    
    if (!roles.includes(req.user.role)) {
      sendErrorResponse(res, 403, 'FORBIDDEN', 'Insufficient permissions');
      return;
    }
    
    next();
  };
};

// 团队成员权限检查中间件
export const requireTeamMember = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    sendErrorResponse(res, 401, 'UNAUTHORIZED', 'Authentication required');
    return;
  }
  
  const teamId = req.params.teamId || req.body.teamId;
  
  if (!teamId) {
    sendErrorResponse(res, 400, 'BAD_REQUEST', 'Team ID is required');
    return;
  }
  
  // 管理员可以访问所有团队
  if (req.user.role === 'admin') {
    next();
    return;
  }
  
  // 检查用户是否属于该团队
  if (req.user.teamId !== teamId) {
    sendErrorResponse(res, 403, 'FORBIDDEN', 'Access denied to this team');
    return;
  }
  
  next();
};

// 资源所有者权限检查中间件
export const requireOwnership = (userIdField: string = 'userId') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      sendErrorResponse(res, 401, 'UNAUTHORIZED', 'Authentication required');
      return;
    }
    
    const resourceUserId = req.params[userIdField] || req.body[userIdField];
    
    if (!resourceUserId) {
      sendErrorResponse(res, 400, 'BAD_REQUEST', `${userIdField} is required`);
      return;
    }
    
    // 管理员可以访问所有资源
    if (req.user.role === 'admin') {
      next();
      return;
    }
    
    // 检查用户是否拥有该资源
    if (req.user.userId !== resourceUserId) {
      sendErrorResponse(res, 403, 'FORBIDDEN', 'Access denied to this resource');
      return;
    }
    
    next();
  };
};
