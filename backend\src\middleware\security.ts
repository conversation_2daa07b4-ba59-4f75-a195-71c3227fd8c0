import helmet from 'helmet';
import config from '../config';

// 安全中间件配置
const securityOptions = helmet({
  // 内容安全策略
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      fontSrc: ["'self'", 'https://fonts.gstatic.com'],
      imgSrc: ["'self'", 'data:', 'https:'],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", 'https://api.openai.com', 'https://api.anthropic.com'],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: config.server.env === 'production' ? [] : null,
    },
  },

  // 跨域嵌入保护
  crossOriginEmbedderPolicy: false,

  // 跨域资源策略
  crossOriginResourcePolicy: {
    policy: 'cross-origin',
  },

  // DNS 预取控制
  dnsPrefetchControl: {
    allow: false,
  },

  // 下载选项
  ieNoOpen: true,

  // MIME 类型嗅探保护
  noSniff: true,

  // 来源策略
  originAgentCluster: true,

  // 权限策略
  permittedCrossDomainPolicies: false,

  // 引用策略
  referrerPolicy: {
    policy: ['no-referrer', 'strict-origin-when-cross-origin'],
  },

  // 严格传输安全
  hsts: config.server.env === 'production' ? {
    maxAge: 31536000, // 1 年
    includeSubDomains: true,
    preload: true,
  } : false,

  // X-Frame-Options
  frameguard: {
    action: 'deny',
  },

  // X-Powered-By 头移除
  hidePoweredBy: true,

  // X-XSS-Protection
  xssFilter: true,
});

export default securityOptions;
