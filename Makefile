# PromptForge Makefile

.PHONY: help dev build start stop clean logs test lint format install

# 默认目标
help:
	@echo "PromptForge 开发工具"
	@echo ""
	@echo "可用命令:"
	@echo "  dev        - 启动开发环境"
	@echo "  build      - 构建所有服务"
	@echo "  start      - 启动生产环境"
	@echo "  stop       - 停止所有服务"
	@echo "  clean      - 清理容器和数据卷"
	@echo "  logs       - 查看服务日志"
	@echo "  test       - 运行测试"
	@echo "  lint       - 代码检查"
	@echo "  format     - 代码格式化"
	@echo "  install    - 安装依赖"
	@echo "  db-migrate - 运行数据库迁移"
	@echo "  db-seed    - 填充测试数据"

# 开发环境
dev:
	@echo "启动开发环境..."
	docker-compose up -d postgres redis minio elasticsearch
	@echo "等待数据库启动..."
	sleep 10
	@echo "开发环境已启动"
	@echo "PostgreSQL: localhost:5432"
	@echo "Redis: localhost:6379"
	@echo "MinIO: localhost:9000 (console: localhost:9001)"
	@echo "Elasticsearch: localhost:9200"

# 启动完整开发环境（包括前后端）
dev-full:
	@echo "启动完整开发环境..."
	docker-compose up -d
	@echo "完整开发环境已启动"
	@echo "前端: http://localhost:3000"
	@echo "后端: http://localhost:3001"

# 构建所有服务
build:
	@echo "构建所有服务..."
	docker-compose build

# 生产环境
start:
	@echo "启动生产环境..."
	docker-compose -f docker-compose.prod.yml up -d

# 停止服务
stop:
	@echo "停止所有服务..."
	docker-compose down
	docker-compose -f docker-compose.prod.yml down

# 清理
clean:
	@echo "清理容器和数据卷..."
	docker-compose down -v
	docker system prune -f

# 查看日志
logs:
	docker-compose logs -f

# 查看特定服务日志
logs-backend:
	docker-compose logs -f backend

logs-frontend:
	docker-compose logs -f frontend

logs-postgres:
	docker-compose logs -f postgres

# 安装依赖
install:
	@echo "安装前端依赖..."
	cd frontend && npm install
	@echo "安装后端依赖..."
	cd backend && npm install

# 运行测试
test:
	@echo "运行后端测试..."
	cd backend && npm test
	@echo "运行前端测试..."
	cd frontend && npm test

# 代码检查
lint:
	@echo "检查后端代码..."
	cd backend && npm run lint
	@echo "检查前端代码..."
	cd frontend && npm run lint

# 代码格式化
format:
	@echo "格式化后端代码..."
	cd backend && npm run format
	@echo "格式化前端代码..."
	cd frontend && npm run format

# 数据库操作
db-migrate:
	@echo "运行数据库迁移..."
	cd backend && npm run migrate

db-seed:
	@echo "填充测试数据..."
	cd backend && npm run seed

db-reset:
	@echo "重置数据库..."
	docker-compose exec postgres psql -U promptforge -d promptforge -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"
	$(MAKE) db-migrate
	$(MAKE) db-seed

# 进入容器
shell-backend:
	docker-compose exec backend sh

shell-frontend:
	docker-compose exec frontend sh

shell-postgres:
	docker-compose exec postgres psql -U promptforge -d promptforge

shell-redis:
	docker-compose exec redis redis-cli

# 监控
monitor:
	@echo "系统监控信息:"
	@echo "Docker 容器状态:"
	docker-compose ps
	@echo ""
	@echo "系统资源使用:"
	docker stats --no-stream

# 备份数据库
backup:
	@echo "备份数据库..."
	docker-compose exec postgres pg_dump -U promptforge promptforge > backup_$(shell date +%Y%m%d_%H%M%S).sql

# 恢复数据库
restore:
	@echo "请指定备份文件: make restore-file BACKUP_FILE=backup_file.sql"

restore-file:
	@echo "恢复数据库从 $(BACKUP_FILE)..."
	docker-compose exec -T postgres psql -U promptforge -d promptforge < $(BACKUP_FILE)

# 健康检查
health:
	@echo "检查服务健康状态..."
	@echo "PostgreSQL:"
	@docker-compose exec postgres pg_isready -U promptforge || echo "PostgreSQL 不可用"
	@echo "Redis:"
	@docker-compose exec redis redis-cli ping || echo "Redis 不可用"
	@echo "MinIO:"
	@curl -s http://localhost:9000/minio/health/live || echo "MinIO 不可用"

# 更新依赖
update:
	@echo "更新前端依赖..."
	cd frontend && npm update
	@echo "更新后端依赖..."
	cd backend && npm update

# 生成 API 文档
docs:
	@echo "生成 API 文档..."
	cd backend && npm run docs

# 部署相关
deploy-staging:
	@echo "部署到测试环境..."
	# 这里添加部署到测试环境的命令

deploy-production:
	@echo "部署到生产环境..."
	# 这里添加部署到生产环境的命令
