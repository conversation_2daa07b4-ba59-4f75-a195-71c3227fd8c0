import { Router, Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { 
  generateTokenPair, 
  verifyRefreshToken, 
  hashPassword, 
  verifyPassword,
  validatePasswordStrength,
  validateEmail 
} from '../utils/auth';
import { sendSuccessResponse, sendErrorResponse } from '../utils/response';
import { authenticate } from '../middleware/auth';
// import prisma from '../config/database';

const router = Router();

// 用户注册
router.post('/register', [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long'),
  body('name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters'),
], async (req: Request, res: Response) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendErrorResponse(res, 400, 'VALIDATION_ERROR', 'Invalid input data', {
        errors: errors.array()
      });
    }

    const { email, password, name } = req.body;

    // 验证邮箱格式
    if (!validateEmail(email)) {
      return sendErrorResponse(res, 400, 'INVALID_EMAIL', 'Invalid email format');
    }

    // 验证密码强度
    const passwordValidation = validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      return sendErrorResponse(res, 400, 'WEAK_PASSWORD', 'Password does not meet requirements', {
        errors: passwordValidation.errors
      });
    }

    // 检查邮箱是否已存在 (暂时使用模拟检查)
    // const existingUser = await prisma.user.findUnique({ where: { email } });
    // if (existingUser) {
    //   return sendErrorResponse(res, 409, 'EMAIL_EXISTS', 'Email already registered');
    // }

    // 模拟检查：如果邮箱是 <EMAIL> 则认为已存在
    if (email === '<EMAIL>') {
      return sendErrorResponse(res, 409, 'EMAIL_EXISTS', 'Email already registered');
    }

    // 哈希密码
    const hashedPassword = await hashPassword(password);
    console.log('Password hashed for user:', email, 'Hash length:', hashedPassword.length); // 临时使用变量

    // 创建用户 (暂时使用模拟数据)
    // const user = await prisma.user.create({
    //   data: {
    //     email,
    //     username: email.split('@')[0],
    //     passwordHash: hashedPassword,
    //     firstName: name,
    //     isActive: true,
    //     isVerified: false,
    //   },
    //   select: {
    //     id: true,
    //     email: true,
    //     username: true,
    //     firstName: true,
    //     lastName: true,
    //     isActive: true,
    //     isVerified: true,
    //     createdAt: true,
    //     updatedAt: true,
    //   },
    // });

    // 模拟用户数据
    const user = {
      id: `user_${Date.now()}`,
      email,
      username: email.split('@')[0],
      firstName: name,
      lastName: null,
      isActive: true,
      isVerified: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // 生成令牌
    const tokens = generateTokenPair({
      userId: user.id,
      email: user.email,
      role: 'user', // 默认角色
    });

    return sendSuccessResponse(res, {
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        name: user.firstName || '',
        firstName: user.firstName,
        lastName: user.lastName,
        isActive: user.isActive,
        isVerified: user.isVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
      tokens,
    }, 'User registered successfully', 201);
  } catch (error) {
    console.error('Registration error:', error);
    return sendErrorResponse(res, 500, 'INTERNAL_ERROR', 'Registration failed');
  }
});

// 用户登录
router.post('/login', [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
], async (req: Request, res: Response) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendErrorResponse(res, 400, 'VALIDATION_ERROR', 'Invalid input data', {
        errors: errors.array()
      });
    }

    const { email, password } = req.body;

    // 查找用户 (暂时使用模拟数据，支持多个预设用户)
    // const user = await prisma.user.findUnique({
    //   where: { email },
    //   select: {
    //     id: true,
    //     email: true,
    //     username: true,
    //     passwordHash: true,
    //     firstName: true,
    //     lastName: true,
    //     isActive: true,
    //     isVerified: true,
    //     createdAt: true,
    //     updatedAt: true,
    //   },
    // });

    // 模拟用户数据库 - 支持多个预设用户
    const mockUsers = [
      {
        id: 'user_admin_123',
        email: '<EMAIL>',
        username: 'admin',
        password: 'admin123',
        firstName: 'Admin',
        lastName: 'User',
      },
      {
        id: 'user_test_456',
        email: '<EMAIL>',
        username: 'test',
        password: 'test123',
        firstName: 'Test',
        lastName: 'User',
      },
      {
        id: 'user_frontend_789',
        email: '<EMAIL>',
        username: 'frontend',
        password: 'frontend123',
        firstName: 'Test',
        lastName: 'User',
      },
    ];

    // 查找用户
    const mockUser = mockUsers.find(u => u.email === email);
    if (!mockUser) {
      return sendErrorResponse(res, 401, 'INVALID_CREDENTIALS', 'Invalid email or password');
    }

    const user = {
      id: mockUser.id,
      email: mockUser.email,
      username: mockUser.username,
      passwordHash: await hashPassword(mockUser.password), // 模拟密码哈希
      firstName: mockUser.firstName,
      lastName: mockUser.lastName,
      isActive: true,
      isVerified: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // 验证密码
    const isValidPassword = await verifyPassword(password, user.passwordHash);
    if (!isValidPassword) {
      return sendErrorResponse(res, 401, 'INVALID_CREDENTIALS', 'Invalid email or password');
    }

    // 更新最后登录时间 (暂时跳过数据库操作)
    // await prisma.user.update({
    //   where: { id: user.id },
    //   data: { lastLoginAt: new Date() },
    // });

    // 生成令牌
    const tokens = generateTokenPair({
      userId: user.id,
      email: user.email,
      role: 'user', // 默认角色
    });

    return sendSuccessResponse(res, {
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        name: user.firstName || '',
        firstName: user.firstName,
        lastName: user.lastName,
        isActive: user.isActive,
        isVerified: user.isVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
      tokens,
    }, 'Login successful', 200);
  } catch (error) {
    console.error('Login error:', error);
    return sendErrorResponse(res, 500, 'INTERNAL_ERROR', 'Login failed');
  }
});

// 刷新令牌
router.post('/refresh', [
  body('refreshToken')
    .notEmpty()
    .withMessage('Refresh token is required'),
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendErrorResponse(res, 400, 'VALIDATION_ERROR', 'Invalid input data', {
        errors: errors.array()
      });
    }

    const { refreshToken } = req.body;

    // 验证刷新令牌
    const payload = verifyRefreshToken(refreshToken);

    // 验证用户是否仍然存在且活跃 (暂时跳过数据库检查)
    // const user = await prisma.user.findUnique({
    //   where: { id: payload.userId },
    //   select: { id: true, isActive: true }
    // });
    // if (!user || !user.isActive) {
    //   return sendErrorResponse(res, 401, 'INVALID_TOKEN', 'User not found or inactive');
    // }

    // 生成新的令牌对
    const tokens = generateTokenPair({
      userId: payload.userId,
      email: payload.email,
      role: payload.role,
      teamId: payload.teamId,
    });

    return sendSuccessResponse(res, { tokens }, 'Token refreshed successfully', 200);
  } catch (error) {
    console.error('Token refresh error:', error);
    return sendErrorResponse(res, 401, 'INVALID_TOKEN', 'Invalid or expired refresh token');
  }
});

// 获取当前用户信息
router.get('/me', authenticate, async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return sendErrorResponse(res, 401, 'UNAUTHORIZED', 'User not authenticated');
    }

    // 从数据库获取最新用户信息 (暂时使用模拟数据)
    // const user = await prisma.user.findUnique({
    //   where: { id: req.user.userId },
    //   select: {
    //     id: true,
    //     email: true,
    //     username: true,
    //     firstName: true,
    //     lastName: true,
    //     avatarUrl: true,
    //     isActive: true,
    //     isVerified: true,
    //     lastLoginAt: true,
    //     createdAt: true,
    //     updatedAt: true
    //   }
    // });

    // if (!user) {
    //   return sendErrorResponse(res, 404, 'USER_NOT_FOUND', 'User not found');
    // }

    // 模拟用户数据
    const user = {
      id: req.user.userId,
      email: req.user.email,
      username: req.user.email.split('@')[0],
      firstName: 'Test',
      lastName: 'User',
      avatarUrl: null,
      isActive: true,
      isVerified: true,
      lastLoginAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return sendSuccessResponse(res, {
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        name: user.firstName || '',
        firstName: user.firstName,
        lastName: user.lastName,
        avatarUrl: user.avatarUrl,
        isActive: user.isActive,
        isVerified: user.isVerified,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      }
    }, 'User information retrieved successfully', 200);
  } catch (error) {
    console.error('Get user info error:', error);
    return sendErrorResponse(res, 500, 'INTERNAL_ERROR', 'Failed to retrieve user information');
  }
});

// 用户登出（可选实现，主要在前端清除令牌）
router.post('/logout', authenticate, async (_req: Request, res: Response) => {
  try {
    // TODO: 可以在这里实现令牌黑名单机制
    // 将当前令牌加入黑名单，防止被继续使用

    return sendSuccessResponse(res, {}, 'Logout successful', 200);
  } catch (error) {
    console.error('Logout error:', error);
    return sendErrorResponse(res, 500, 'INTERNAL_ERROR', 'Logout failed');
  }
});

export default router;
