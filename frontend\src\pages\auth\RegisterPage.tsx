import React, { useEffect } from 'react';
import { Form, Input, But<PERSON>, Card, Typography, Alert, Divider, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { register, clearError } from '../../store/slices/authSlice';
import styles from './AuthPages.module.css';

const { Title, Text } = Typography;

interface RegisterFormData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  agreement: boolean;
}

const RegisterPage: React.FC = () => {
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { isLoading, error, isAuthenticated } = useAppSelector(state => state.auth);

  useEffect(() => {
    // 清除之前的错误
    dispatch(clearError());
  }, [dispatch]);

  useEffect(() => {
    // 如果已经登录，重定向到首页
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  const handleSubmit = async (values: RegisterFormData) => {
    try {
      const { name, email, password } = values;
      await dispatch(register({ name, email, password })).unwrap();
      // 注册成功后会通过 useEffect 重定向
    } catch (error) {
      // 错误已经在 Redux 中处理
      console.error('Registration failed:', error);
    }
  };

  const validatePassword = (_: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error('请输入密码'));
    }
    
    const errors: string[] = [];
    
    if (value.length < 8) {
      errors.push('至少8位字符');
    }
    
    if (!/[A-Z]/.test(value)) {
      errors.push('包含大写字母');
    }
    
    if (!/[a-z]/.test(value)) {
      errors.push('包含小写字母');
    }
    
    if (!/\d/.test(value)) {
      errors.push('包含数字');
    }
    
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(value)) {
      errors.push('包含特殊字符');
    }
    
    if (errors.length > 0) {
      return Promise.reject(new Error(`密码必须${errors.join('、')}`));
    }
    
    return Promise.resolve();
  };

  const validateConfirmPassword = (_: any, value: string) => {
    const password = form.getFieldValue('password');
    if (value && value !== password) {
      return Promise.reject(new Error('两次输入的密码不一致'));
    }
    return Promise.resolve();
  };

  return (
    <div className={styles.authContainer}>
      <div className={styles.authCard}>
        <Card>
          <div className={styles.authHeader}>
            <Title level={2} className={styles.authTitle}>
              注册 PromptForge
            </Title>
            <Text type="secondary">
              创建您的账户，开始使用 PromptForge
            </Text>
          </div>

          {error && (
            <Alert
              message="注册失败"
              description={error}
              type="error"
              showIcon
              closable
              onClose={() => dispatch(clearError())}
              style={{ marginBottom: 24 }}
            />
          )}

          <Form
            form={form}
            name="register"
            onFinish={handleSubmit}
            layout="vertical"
            size="large"
            autoComplete="off"
          >
            <Form.Item
              name="name"
              label="姓名"
              rules={[
                { required: true, message: '请输入姓名' },
                { min: 2, message: '姓名至少2位字符' },
                { max: 50, message: '姓名最多50位字符' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="请输入您的姓名"
                autoComplete="name"
              />
            </Form.Item>

            <Form.Item
              name="email"
              label="邮箱"
              rules={[
                { required: true, message: '请输入邮箱地址' },
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="请输入邮箱地址"
                autoComplete="email"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="密码"
              rules={[{ validator: validatePassword }]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请输入密码"
                autoComplete="new-password"
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              label="确认密码"
              dependencies={['password']}
              rules={[
                { required: true, message: '请确认密码' },
                { validator: validateConfirmPassword },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请再次输入密码"
                autoComplete="new-password"
              />
            </Form.Item>

            <Form.Item
              name="agreement"
              valuePropName="checked"
              rules={[
                { required: true, message: '请阅读并同意服务条款' },
              ]}
            >
              <Checkbox>
                我已阅读并同意{' '}
                <Link to="/terms" target="_blank" className={styles.authLink}>
                  服务条款
                </Link>{' '}
                和{' '}
                <Link to="/privacy" target="_blank" className={styles.authLink}>
                  隐私政策
                </Link>
              </Checkbox>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={isLoading}
                block
                className={styles.authButton}
              >
                {isLoading ? '注册中...' : '注册'}
              </Button>
            </Form.Item>
          </Form>

          <Divider>
            <Text type="secondary">已有账户？</Text>
          </Divider>

          <div className={styles.authLinks}>
            <Text type="secondary">
              <Link to="/auth/login" className={styles.authLink}>
                立即登录
              </Link>
            </Text>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default RegisterPage;
