# 服务器配置
NODE_ENV=development
PORT=3001
HOST=localhost

# 数据库配置
DATABASE_URL=postgresql://promptforge:promptforge123@localhost:5432/promptforge

# Redis 配置
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 加密配置
BCRYPT_ROUNDS=12

# CORS 配置
CORS_ORIGIN=http://localhost:3000

# 限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads
ALLOWED_FILE_TYPES=.txt,.md,.json,.csv

# LLM API 配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
ANTHROPIC_API_KEY=your-anthropic-api-key
GOOGLE_API_KEY=your-google-api-key

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>

# MinIO 配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET=promptforge
MINIO_USE_SSL=false

# Elasticsearch 配置
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_INDEX=promptforge

# 监控配置
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info

# 第三方登录配置
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=100

# API 文档配置
SWAGGER_ENABLED=true
SWAGGER_PATH=/api-docs
