{"name": "promptforge-backend", "version": "0.1.0", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node -r tsconfig-paths/register src/index.ts", "build": "tsc && tsc-alias", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write \"src/**/*.{ts,js,json}\"", "format:check": "prettier --check \"src/**/*.{ts,js,json}\"", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "docs": "swagger-jsdoc -d swaggerDef.js -o swagger.json src/routes/*.ts", "clean": "rm -rf dist"}, "keywords": ["llm", "prompt", "ai", "management", "api"], "author": "PromptForge Team", "license": "MIT", "description": "PromptForge 后端服务", "_moduleAliases": {"@": "dist"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/ioredis": "^4.28.10", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^2.0.0", "@types/node": "^24.0.14", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "concurrently": "^9.2.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "dependencies": {"@prisma/client": "^6.12.0", "bcryptjs": "^3.0.2", "compression": "^1.8.1", "cors": "^2.8.5", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "module-alias": "^2.2.3", "morgan": "^1.10.1", "prisma": "^6.12.0", "redis": "^5.6.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "winston": "^3.17.0"}}