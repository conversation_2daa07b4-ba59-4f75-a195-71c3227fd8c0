/* 认证页面容器 */
.authContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

/* 认证卡片 */
.authCard {
  width: 100%;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
}

.authCard .ant-card {
  border: none;
  border-radius: 12px;
}

.authCard .ant-card-body {
  padding: 40px 32px;
}

/* 认证头部 */
.authHeader {
  text-align: center;
  margin-bottom: 32px;
}

.authTitle {
  margin-bottom: 8px !important;
  color: #1f2937;
  font-weight: 600;
}

.authHeader .ant-typography {
  color: #6b7280;
}

/* 认证按钮 */
.authButton {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.authButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.authButton:focus {
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 认证链接 */
.authLinks {
  text-align: center;
}

.authLink {
  color: #667eea;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.3s ease;
}

.authLink:hover {
  color: #764ba2;
  text-decoration: underline;
}

/* 表单样式优化 */
.authCard .ant-form-item-label > label {
  font-weight: 500;
  color: #374151;
}

.authCard .ant-input,
.authCard .ant-input-password {
  height: 44px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.authCard .ant-input:focus,
.authCard .ant-input-password:focus,
.authCard .ant-input-focused,
.authCard .ant-input-password-focused {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.authCard .ant-input-prefix {
  color: #9ca3af;
}

/* 分割线样式 */
.authCard .ant-divider-horizontal.ant-divider-with-text {
  margin: 24px 0;
}

.authCard .ant-divider-inner-text {
  color: #9ca3af;
  font-size: 14px;
}

/* 复选框样式 */
.authCard .ant-checkbox-wrapper {
  font-size: 14px;
  color: #6b7280;
}

.authCard .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #667eea;
  border-color: #667eea;
}

/* 警告框样式 */
.authCard .ant-alert {
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .authContainer {
    padding: 16px;
  }
  
  .authCard {
    max-width: 100%;
  }
  
  .authCard .ant-card-body {
    padding: 24px 20px;
  }
  
  .authHeader {
    margin-bottom: 24px;
  }
  
  .authTitle {
    font-size: 24px !important;
  }
}

/* 加载状态优化 */
.authButton.ant-btn-loading {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.authButton.ant-btn-loading:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 表单验证错误样式 */
.authCard .ant-form-item-has-error .ant-input,
.authCard .ant-form-item-has-error .ant-input-password {
  border-color: #ef4444;
}

.authCard .ant-form-item-has-error .ant-input:focus,
.authCard .ant-form-item-has-error .ant-input-password:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

/* 成功状态样式 */
.authCard .ant-form-item-has-success .ant-input,
.authCard .ant-form-item-has-success .ant-input-password {
  border-color: #10b981;
}

.authCard .ant-form-item-has-success .ant-input:focus,
.authCard .ant-form-item-has-success .ant-input-password:focus {
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
}
