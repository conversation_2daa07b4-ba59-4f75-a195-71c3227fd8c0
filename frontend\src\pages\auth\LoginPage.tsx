import React, { useEffect } from 'react';
import { Form, Input, But<PERSON>, Card, Typography, Alert, Divider, Space } from 'antd';
import { LockOutlined, MailOutlined } from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { login, clearError } from '../../store/slices/authSlice';
import styles from './AuthPages.module.css';

const { Title, Text } = Typography;

interface LoginFormData {
  email: string;
  password: string;
}

const LoginPage: React.FC = () => {
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { isLoading, error, isAuthenticated } = useAppSelector(state => state.auth);

  useEffect(() => {
    // 清除之前的错误
    dispatch(clearError());
  }, [dispatch]);

  useEffect(() => {
    // 如果已经登录，重定向到首页
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  const handleSubmit = async (values: LoginFormData) => {
    try {
      await dispatch(login(values)).unwrap();
      // 登录成功后会通过 useEffect 重定向
    } catch (error) {
      // 错误已经在 Redux 中处理
      console.error('Login failed:', error);
    }
  };

  return (
    <div className={styles.authContainer}>
      <div className={styles.authCard}>
        <Card>
          <div className={styles.authHeader}>
            <Title level={2} className={styles.authTitle}>
              登录 PromptForge
            </Title>
            <Text type="secondary">
              欢迎回来！请登录您的账户
            </Text>
          </div>

          {error && (
            <Alert
              message="登录失败"
              description={error}
              type="error"
              showIcon
              closable
              onClose={() => dispatch(clearError())}
              style={{ marginBottom: 24 }}
            />
          )}

          <Form
            form={form}
            name="login"
            onFinish={handleSubmit}
            layout="vertical"
            size="large"
            autoComplete="off"
          >
            <Form.Item
              name="email"
              label="邮箱"
              rules={[
                { required: true, message: '请输入邮箱地址' },
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="请输入邮箱地址"
                autoComplete="email"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6位字符' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请输入密码"
                autoComplete="current-password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={isLoading}
                block
                className={styles.authButton}
              >
                {isLoading ? '登录中...' : '登录'}
              </Button>
            </Form.Item>
          </Form>

          <Divider>
            <Text type="secondary">其他选项</Text>
          </Divider>

          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <div className={styles.authLinks}>
              <Text type="secondary">
                还没有账户？{' '}
                <Link to="/auth/register" className={styles.authLink}>
                  立即注册
                </Link>
              </Text>
            </div>

            <div className={styles.authLinks}>
              <Link to="/auth/forgot-password" className={styles.authLink}>
                忘记密码？
              </Link>
            </div>
          </Space>
        </Card>
      </div>
    </div>
  );
};

export default LoginPage;
