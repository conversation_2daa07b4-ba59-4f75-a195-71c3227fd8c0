// 用户相关类型
export interface User {
  id: string;
  email: string;
  name: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
  role: string;
  isActive: boolean;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// 团队相关类型
export interface Team {
  id: string;
  name: string;
  description?: string;
  avatarUrl?: string;
  ownerId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TeamMember {
  id: string;
  teamId: string;
  userId: string;
  role: 'owner' | 'admin' | 'member' | 'viewer';
  joinedAt: string;
  user: User;
}

// Prompt 相关类型
export interface Prompt {
  id: string;
  name: string;
  description?: string;
  content: string;
  tags: string[];
  category?: string;
  language: string;
  modelConfig?: ModelConfig;
  ownerId: string;
  teamId?: string;
  isPublic: boolean;
  isTemplate: boolean;
  forkFromId?: string;
  versionCount: number;
  createdAt: string;
  updatedAt: string;
  owner: User;
  team?: Team;
}

export interface PromptVersion {
  id: string;
  promptId: string;
  versionNumber: number;
  content: string;
  changeMessage?: string;
  authorId: string;
  parentVersionId?: string;
  isCurrent: boolean;
  createdAt: string;
  author: User;
}

export interface PromptTemplate {
  id: string;
  name: string;
  description?: string;
  content: string;
  variables?: Record<string, TemplateVariable>;
  category?: string;
  isSystem: boolean;
  createdBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface TemplateVariable {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description?: string;
  default?: any;
  required?: boolean;
  options?: string[];
}

// 模型配置类型
export interface ModelConfig {
  provider: 'openai' | 'anthropic' | 'google' | 'local';
  model: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stopSequences?: string[];
}

// 测试相关类型
export interface TestSession {
  id: string;
  promptId: string;
  promptVersionId?: string;
  name?: string;
  modelConfig: ModelConfig;
  status: 'pending' | 'running' | 'completed' | 'failed';
  createdBy: string;
  createdAt: string;
  completedAt?: string;
  testCases: TestCase[];
}

export interface TestCase {
  id: string;
  sessionId: string;
  inputData: Record<string, any>;
  expectedOutput?: string;
  actualOutput?: string;
  executionTime?: number;
  tokensUsed?: number;
  cost?: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  errorMessage?: string;
  createdAt: string;
  completedAt?: string;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
  requestId: string;
}

export interface PaginatedResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 通用类型
export interface SelectOption {
  label: string;
  value: string | number;
  disabled?: boolean;
}

export interface TableColumn {
  key: string;
  title: string;
  dataIndex?: string;
  width?: number;
  fixed?: 'left' | 'right';
  sorter?: boolean;
  render?: (value: any, record: any, index: number) => React.ReactNode;
}

// 路由类型
export interface RouteConfig {
  path: string;
  component: React.ComponentType;
  exact?: boolean;
  requireAuth?: boolean;
  roles?: string[];
  title?: string;
}

// 表单类型
export interface FormField {
  name: string;
  label: string;
  type: 'input' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'upload' | 'editor';
  required?: boolean;
  placeholder?: string;
  options?: SelectOption[];
  rules?: any[];
  disabled?: boolean;
  hidden?: boolean;
}

// 主题类型
export interface ThemeConfig {
  primaryColor: string;
  borderRadius: number;
  colorBgContainer: string;
  colorText: string;
  colorTextSecondary: string;
  colorBorder: string;
  colorBgLayout: string;
}

// 应用状态类型
export interface AppState {
  theme: 'light' | 'dark';
  language: 'zh' | 'en';
  sidebarCollapsed: boolean;
  loading: boolean;
  error: string | null;
}
