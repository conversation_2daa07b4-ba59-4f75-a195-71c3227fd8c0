module.exports = {
  // TypeScript 和 JavaScript 文件
  '*.{ts,tsx,js,jsx}': [
    'eslint --fix',
    'prettier --write',
    'git add',
  ],
  
  // JSON 文件
  '*.json': [
    'prettier --write',
    'git add',
  ],
  
  // Markdown 文件
  '*.md': [
    'prettier --write',
    'git add',
  ],
  
  // YAML 文件
  '*.{yml,yaml}': [
    'prettier --write',
    'git add',
  ],
  
  // CSS 和样式文件
  '*.{css,scss,sass,less}': [
    'prettier --write',
    'git add',
  ],
  
  // HTML 文件
  '*.html': [
    'prettier --write',
    'git add',
  ],
  
  // 包配置文件
  'package.json': [
    'prettier --write',
    'git add',
  ],
  
  // 特定项目文件的处理
  'frontend/**/*.{ts,tsx,js,jsx}': [
    'cd frontend && npm run lint:fix',
    'cd frontend && npm run format',
    'git add',
  ],
  
  'backend/**/*.{ts,js}': [
    'cd backend && npm run lint:fix',
    'cd backend && npm run format',
    'git add',
  ],
  
  // 测试文件
  '*.{test,spec}.{ts,tsx,js,jsx}': [
    'eslint --fix',
    'prettier --write',
    'git add',
  ],
};
