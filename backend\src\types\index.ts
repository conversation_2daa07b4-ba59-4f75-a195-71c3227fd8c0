import { Request } from 'express';

// 用户相关类型
export interface User {
  id: string;
  email: string;
  username: string;
  passwordHash?: string;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
  isActive: boolean;
  isVerified: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserInput {
  email: string;
  username: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

export interface UpdateUserInput {
  username?: string;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
}

export interface LoginInput {
  email: string;
  password: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

// 团队相关类型
export interface Team {
  id: string;
  name: string;
  description?: string;
  avatarUrl?: string;
  ownerId: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface TeamMember {
  id: string;
  teamId: string;
  userId: string;
  role: TeamRole;
  joinedAt: Date;
}

export type TeamRole = 'owner' | 'admin' | 'member' | 'viewer';

export interface CreateTeamInput {
  name: string;
  description?: string;
}

export interface UpdateTeamInput {
  name?: string;
  description?: string;
  avatarUrl?: string;
}

export interface AddTeamMemberInput {
  email: string;
  role: TeamRole;
}

// Prompt 相关类型
export interface Prompt {
  id: string;
  name: string;
  description?: string;
  content: string;
  tags: string[];
  category?: string;
  language: string;
  modelConfig?: ModelConfig;
  ownerId: string;
  teamId?: string;
  isPublic: boolean;
  isTemplate: boolean;
  forkFromId?: string;
  versionCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface PromptVersion {
  id: string;
  promptId: string;
  versionNumber: number;
  content: string;
  changeMessage?: string;
  authorId: string;
  parentVersionId?: string;
  isCurrent: boolean;
  createdAt: Date;
}

export interface CreatePromptInput {
  name: string;
  description?: string;
  content: string;
  tags?: string[];
  category?: string;
  language?: string;
  modelConfig?: ModelConfig;
  teamId?: string;
  isPublic?: boolean;
  isTemplate?: boolean;
}

export interface UpdatePromptInput {
  name?: string;
  description?: string;
  content?: string;
  tags?: string[];
  category?: string;
  language?: string;
  modelConfig?: ModelConfig;
  isPublic?: boolean;
}

export interface CreatePromptVersionInput {
  content: string;
  changeMessage?: string;
}

// 模型配置类型
export interface ModelConfig {
  provider: ModelProvider;
  model: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stopSequences?: string[];
}

export type ModelProvider = 'openai' | 'anthropic' | 'google' | 'local';

// 测试相关类型
export interface TestSession {
  id: string;
  promptId: string;
  promptVersionId?: string;
  name?: string;
  modelConfig: ModelConfig;
  status: TestStatus;
  createdBy: string;
  createdAt: Date;
  completedAt?: Date;
}

export interface TestCase {
  id: string;
  sessionId: string;
  inputData: Record<string, any>;
  expectedOutput?: string;
  actualOutput?: string;
  executionTime?: number;
  tokensUsed?: number;
  cost?: number;
  status: TestStatus;
  errorMessage?: string;
  createdAt: Date;
  completedAt?: Date;
}

export type TestStatus = 'pending' | 'running' | 'completed' | 'failed';

export interface CreateTestSessionInput {
  promptId: string;
  promptVersionId?: string;
  name?: string;
  modelConfig: ModelConfig;
}

export interface RunSingleTestInput {
  promptId: string;
  input: Record<string, any>;
  modelConfig: ModelConfig;
}

export interface RunBatchTestInput {
  promptId: string;
  testCases: Array<{
    input: Record<string, any>;
    expectedOutput?: string;
  }>;
  modelConfig: ModelConfig;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: ApiError;
  timestamp: string;
  requestId: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

export interface PaginatedResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface PaginationQuery {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface SearchQuery extends PaginationQuery {
  search?: string;
  filters?: Record<string, any>;
}

// Express 扩展类型
export interface AuthenticatedRequest extends Omit<Request, 'user'> {
  user?: User;
  userId?: string;
}

// 中间件类型
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

// 文件上传类型
export interface FileUpload {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  path: string;
  uploadedBy: string;
  createdAt: Date;
}

export interface UploadFileInput {
  file: Express.Multer.File;
  type?: string;
}

// 审计日志类型
export interface AuditLog {
  id: string;
  userId?: string;
  action: string;
  resourceType: string;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
}

// 缓存类型
export interface CacheOptions {
  ttl?: number;
  key?: string;
}

// 邮件类型
export interface EmailOptions {
  to: string | string[];
  subject: string;
  text?: string;
  html?: string;
  template?: string;
  context?: Record<string, any>;
}

// 统计类型
export interface UsageStats {
  totalPrompts: number;
  totalTests: number;
  totalUsers: number;
  totalTeams: number;
  apiCalls: number;
  successRate: number;
}

export interface PromptStats {
  id: string;
  name: string;
  usageCount: number;
  successRate: number;
  avgExecutionTime: number;
  totalCost: number;
  lastUsed?: Date;
}
