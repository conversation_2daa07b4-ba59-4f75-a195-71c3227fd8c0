<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PromptForge Auth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"], input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>PromptForge Authentication Test</h1>
    
    <!-- Login Section -->
    <div class="section">
        <h2>Login Test</h2>
        <div class="form-group">
            <label for="loginEmail">Email:</label>
            <input type="email" id="loginEmail" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label for="loginPassword">Password:</label>
            <input type="password" id="loginPassword" value="admin123">
        </div>
        <button onclick="testLogin()">Login</button>
        <button onclick="testLogin('<EMAIL>', 'test123')">Login as Test User</button>
        <button onclick="testLogin('<EMAIL>', 'frontend123')">Login as Frontend User</button>
    </div>

    <!-- Register Section -->
    <div class="section">
        <h2>Register Test</h2>
        <div class="form-group">
            <label for="regEmail">Email:</label>
            <input type="email" id="regEmail" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label for="regPassword">Password:</label>
            <input type="password" id="regPassword" value="Password123@">
        </div>
        <div class="form-group">
            <label for="regName">Name:</label>
            <input type="text" id="regName" value="New User">
        </div>
        <button onclick="testRegister()">Register</button>
    </div>

    <!-- User Info Section -->
    <div class="section">
        <h2>User Info Test</h2>
        <button onclick="testUserInfo()">Get User Info</button>
        <button onclick="clearTokens()">Clear Tokens</button>
    </div>

    <div id="result" class="result"></div>

    <script>
        const API_BASE = 'http://localhost:3001/api/v1';
        let accessToken = localStorage.getItem('accessToken');
        let refreshToken = localStorage.getItem('refreshToken');

        function showResult(data, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = JSON.stringify(data, null, 2);
            resultDiv.className = `result ${isError ? 'error' : 'success'}`;
        }

        async function testLogin(email = null, password = null) {
            const loginEmail = email || document.getElementById('loginEmail').value;
            const loginPassword = password || document.getElementById('loginPassword').value;
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: loginEmail,
                        password: loginPassword
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    accessToken = data.data.tokens.accessToken;
                    refreshToken = data.data.tokens.refreshToken;
                    localStorage.setItem('accessToken', accessToken);
                    localStorage.setItem('refreshToken', refreshToken);
                    showResult({
                        message: 'Login successful!',
                        user: data.data.user,
                        tokens: {
                            accessToken: accessToken.substring(0, 20) + '...',
                            refreshToken: refreshToken.substring(0, 20) + '...'
                        }
                    });
                } else {
                    showResult(data, true);
                }
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }

        async function testRegister() {
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;
            const name = document.getElementById('regName').value;
            
            try {
                const response = await fetch(`${API_BASE}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password,
                        name: name
                    })
                });

                const data = await response.json();
                showResult(data, !response.ok);
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }

        async function testUserInfo() {
            if (!accessToken) {
                showResult({ error: 'No access token. Please login first.' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/auth/me`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                showResult(data, !response.ok);
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }

        function clearTokens() {
            accessToken = null;
            refreshToken = null;
            localStorage.removeItem('accessToken');
            localStorage.removeItem('refreshToken');
            showResult({ message: 'Tokens cleared successfully' });
        }

        // Show current token status on load
        window.onload = function() {
            if (accessToken) {
                showResult({
                    message: 'Existing tokens found',
                    accessToken: accessToken.substring(0, 20) + '...',
                    refreshToken: refreshToken ? refreshToken.substring(0, 20) + '...' : 'None'
                });
            } else {
                showResult({ message: 'No existing tokens found. Please login.' });
            }
        };
    </script>
</body>
</html>
