# PromptForge

一个专业的 LLM Prompt 管理平台，支持版本控制、参数调试、多模型切换和团队协作。

## 🚀 功能特性

- **智能编辑器**: 基于 Monaco Editor 的专业 Prompt 编辑体验
- **版本控制**: Git-like 的版本管理系统，支持分支、合并、回滚
- **多模型支持**: 统一接口支持 OpenAI、Claude、Gemini 等主流 LLM
- **团队协作**: 完整的权限管理和协作工作流
- **智能测试**: 单次测试、批量测试、A/B 测试
- **数据分析**: 详细的性能分析和成本统计
- **模板系统**: 丰富的预置模板和自定义模板支持

## 🏗️ 技术架构

- **前端**: React 18 + TypeScript + Ant Design
- **后端**: Node.js + Express + TypeScript
- **数据库**: PostgreSQL + Redis
- **部署**: Docker + Kubernetes
- **监控**: Prometheus + Grafana

## 📁 项目结构

```
PromptForge/
├── docs/                   # 项目文档
├── frontend/              # 前端应用
├── backend/               # 后端服务
├── shared/                # 共享代码和类型定义
├── docker/                # Docker 配置
├── k8s/                   # Kubernetes 配置
├── scripts/               # 构建和部署脚本
└── tests/                 # 端到端测试
```

## 🚦 开发状态

当前版本: MVP 开发中

- [x] 项目初始化
- [x] 技术方案设计
- [x] 开发计划制定
- [ ] 基础架构搭建
- [ ] 核心功能开发
- [ ] 测试和部署

## 📋 开发计划

### MVP 阶段 (第1-3个月)

- 基础 Prompt 编辑器
- 用户认证系统
- 单一 LLM 模型支持
- 基础版本控制
- 简单测试功能

### V1.0 阶段 (第4-6个月)

- 多模型支持
- 团队协作功能
- 高级版本控制
- 批量测试和分析

### V2.0 阶段 (第7-12个月)

- AI 驱动优化建议
- 企业级安全功能
- 移动端应用
- VS Code 插件

## 🛠️ 快速开始

### 环境要求

- Node.js 18+
- PostgreSQL 15+
- Redis 7+
- Docker & Docker Compose

### 本地开发

```bash
# 克隆项目
git clone <repository-url>
cd PromptForge

# 安装依赖
npm install

# 启动开发环境
docker-compose up -d

# 启动前端
cd frontend
npm run dev

# 启动后端
cd backend
npm run dev
```

## 📖 文档

- [产品需求文档 (PRD)](./docs/PRD.md)
- [技术方案](./docs/技术方案.md)
- [开发计划](./docs/开发计划.md)
- [API 文档](./docs/api.md) (开发中)
- [部署指南](./docs/deployment.md) (开发中)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 邮箱: [<EMAIL>]

---

⭐ 如果这个项目对您有帮助，请给我们一个 Star！
