import { server } from './app';
import config from './config';
import { log } from './utils/logger';
// import { connectDatabase } from './config/database';
// import { connectRedis } from './config/redis';

// 启动服务器
const startServer = async (): Promise<void> => {
  try {
    // 连接数据库 (暂时禁用，等 Docker 启动后再启用)
    // await connectDatabase();

    // 暂时跳过 Redis 连接用于测试
    // await connectRedis();
    
    // 启动 HTTP 服务器
    server.listen(config.server.port, () => {
      log.info('🚀 Server started successfully');
      log.info(`📍 Environment: ${config.server.env}`);
      log.info(`🌐 Server running at http://localhost:${config.server.port}`);
      log.info(`📚 API Documentation: http://localhost:${config.server.port}/api-docs`);
      log.info(`❤️  Health Check: http://localhost:${config.server.port}/health`);
    });
    
  } catch (error) {
    console.error('Failed to start server:', error);
    log.error('Failed to start server:', error);
    process.exit(1);
  }
};

// 启动应用
startServer().catch(error => {
  console.error('Startup error:', error);
  process.exit(1);
});
