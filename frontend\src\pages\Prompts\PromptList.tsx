import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Avatar,
  Tooltip,
  Dropdown,
  Modal,
  message,
  Row,
  Col,
  Typography,
  Divider,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  EyeOutlined,
  MoreOutlined,
  UserOutlined,
  CalendarOutlined,
  TagOutlined,
  GlobalOutlined,
  LockOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { ColumnsType } from 'antd/es/table';
import { promptApi } from '../../services/api';
import { Prompt, PaginatedResponse } from '../../types';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

const { Search } = Input;
const { Option } = Select;
const { Text, Title } = Typography;

interface PromptListState {
  prompts: Prompt[];
  loading: boolean;
  total: number;
  page: number;
  pageSize: number;
  search: string;
  category: string;
  language: string;
  isPublic?: boolean;
  isTemplate?: boolean;
}

const PromptList: React.FC = () => {
  const navigate = useNavigate();
  const [state, setState] = useState<PromptListState>({
    prompts: [],
    loading: false,
    total: 0,
    page: 1,
    pageSize: 10,
    search: '',
    category: '',
    language: '',
  });

  // 获取 Prompt 列表
  const fetchPrompts = async () => {
    setState(prev => ({ ...prev, loading: true }));
    try {
      const params = {
        page: state.page,
        pageSize: state.pageSize,
        search: state.search || undefined,
        category: state.category || undefined,
        language: state.language || undefined,
        isPublic: state.isPublic,
        isTemplate: state.isTemplate,
      };

      const response = await promptApi.getPrompts(params);
      const data = response.data as PaginatedResponse<Prompt>;

      setState(prev => ({
        ...prev,
        prompts: data.items,
        total: data.total,
        loading: false,
      }));
    } catch (error) {
      console.error('获取 Prompt 列表失败:', error);
      message.error('获取 Prompt 列表失败');
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  useEffect(() => {
    fetchPrompts();
  }, [state.page, state.pageSize, state.search, state.category, state.language, state.isPublic, state.isTemplate]);

  // 搜索处理
  const handleSearch = (value: string) => {
    setState(prev => ({ ...prev, search: value, page: 1 }));
  };

  // 筛选处理
  const handleFilter = (key: string, value: any) => {
    setState(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // 删除 Prompt
  const handleDelete = async (id: string) => {
    try {
      await promptApi.deletePrompt(id);
      message.success('删除成功');
      fetchPrompts();
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 复制 Prompt
  const handleFork = async (id: string) => {
    try {
      await promptApi.forkPrompt(id);
      message.success('复制成功');
      fetchPrompts();
    } catch (error) {
      console.error('复制失败:', error);
      message.error('复制失败');
    }
  };

  // 操作菜单
  const getActionMenu = (record: Prompt) => ({
    items: [
      {
        key: 'view',
        icon: <EyeOutlined />,
        label: '查看详情',
        onClick: () => navigate(`/prompts/${record.id}`),
      },
      {
        key: 'edit',
        icon: <EditOutlined />,
        label: '编辑',
        onClick: () => navigate(`/prompts/${record.id}/edit`),
      },
      {
        key: 'fork',
        icon: <CopyOutlined />,
        label: '复制',
        onClick: () => handleFork(record.id),
      },
      {
        type: 'divider',
      },
      {
        key: 'delete',
        icon: <DeleteOutlined />,
        label: '删除',
        danger: true,
        onClick: () => {
          Modal.confirm({
            title: '确认删除',
            content: `确定要删除 Prompt "${record.name}" 吗？此操作不可恢复。`,
            okText: '删除',
            okType: 'danger',
            cancelText: '取消',
            onOk: () => handleDelete(record.id),
          });
        },
      },
    ],
  });

  // 表格列定义
  const columns: ColumnsType<Prompt> = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text: string, record: Prompt) => (
        <div>
          <div style={{ fontWeight: 500, marginBottom: 4 }}>
            <Button
              type="link"
              style={{ padding: 0, height: 'auto', fontWeight: 500 }}
              onClick={() => navigate(`/prompts/${record.id}`)}
            >
              {text}
            </Button>
          </div>
          {record.description && (
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.description.length > 50
                ? `${record.description.substring(0, 50)}...`
                : record.description}
            </Text>
          )}
        </div>
      ),
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      width: 200,
      render: (tags: string[]) => (
        <div>
          {tags.slice(0, 3).map(tag => (
            <Tag key={tag} style={{ marginBottom: 2 }}>
              {tag}
            </Tag>
          ))}
          {tags.length > 3 && (
            <Tooltip title={tags.slice(3).join(', ')}>
              <Tag>+{tags.length - 3}</Tag>
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category: string) => category && <Tag color="blue">{category}</Tag>,
    },
    {
      title: '语言',
      dataIndex: 'language',
      key: 'language',
      width: 80,
      render: (language: string) => language && <Tag color="green">{language}</Tag>,
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (_, record: Prompt) => (
        <Space>
          {record.isPublic && (
            <Tooltip title="公开">
              <GlobalOutlined style={{ color: '#52c41a' }} />
            </Tooltip>
          )}
          {!record.isPublic && (
            <Tooltip title="私有">
              <LockOutlined style={{ color: '#faad14' }} />
            </Tooltip>
          )}
          {record.isTemplate && (
            <Tooltip title="模板">
              <TagOutlined style={{ color: '#1890ff' }} />
            </Tooltip>
          )}
        </Space>
      ),
    },
    {
      title: '创建者',
      dataIndex: 'owner',
      key: 'owner',
      width: 120,
      render: (owner: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar size="small" icon={<UserOutlined />} style={{ marginRight: 8 }} />
          <Text style={{ fontSize: 12 }}>{owner?.name || '未知'}</Text>
        </div>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 120,
      render: (date: string) => (
        <Tooltip title={new Date(date).toLocaleString()}>
          <Text style={{ fontSize: 12 }}>
            {formatDistanceToNow(new Date(date), { addSuffix: true, locale: zhCN })}
          </Text>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 80,
      fixed: 'right',
      render: (_, record: Prompt) => (
        <Dropdown menu={getActionMenu(record)} trigger={['click']}>
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={3} style={{ margin: 0 }}>
          Prompt 管理
        </Title>
        <Text type="secondary">管理和组织您的 AI Prompt</Text>
      </div>

      {/* 操作栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Space size="middle">
              <Search
                placeholder="搜索 Prompt 名称或描述"
                allowClear
                style={{ width: 300 }}
                onSearch={handleSearch}
                enterButton={<SearchOutlined />}
              />
              <Select
                placeholder="分类"
                allowClear
                style={{ width: 120 }}
                value={state.category || undefined}
                onChange={(value) => handleFilter('category', value)}
              >
                <Option value="general">通用</Option>
                <Option value="coding">编程</Option>
                <Option value="writing">写作</Option>
                <Option value="analysis">分析</Option>
                <Option value="creative">创意</Option>
              </Select>
              <Select
                placeholder="语言"
                allowClear
                style={{ width: 100 }}
                value={state.language || undefined}
                onChange={(value) => handleFilter('language', value)}
              >
                <Option value="zh">中文</Option>
                <Option value="en">英文</Option>
                <Option value="ja">日文</Option>
                <Option value="ko">韩文</Option>
              </Select>
              <Select
                placeholder="状态"
                allowClear
                style={{ width: 100 }}
                onChange={(value) => {
                  if (value === 'public') {
                    handleFilter('isPublic', true);
                  } else if (value === 'private') {
                    handleFilter('isPublic', false);
                  } else {
                    handleFilter('isPublic', undefined);
                  }
                }}
              >
                <Option value="public">公开</Option>
                <Option value="private">私有</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/prompts/new')}
            >
              创建 Prompt
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 数据表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={state.prompts}
          rowKey="id"
          loading={state.loading}
          pagination={{
            current: state.page,
            pageSize: state.pageSize,
            total: state.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setState(prev => ({ ...prev, page, pageSize: pageSize || 10 }));
            },
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
};

export default PromptList;
