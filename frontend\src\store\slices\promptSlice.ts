import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Prompt, PromptVersion, PromptTemplate, PaginatedResponse } from '@/types';
import { promptApi } from '@/services/api';

interface PromptState {
  prompts: Prompt[];
  currentPrompt: Prompt | null;
  promptVersions: PromptVersion[];
  templates: PromptTemplate[];
  isLoading: boolean;
  error: string | null;
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

// 异步 actions
export const fetchPrompts = createAsyncThunk(
  'prompt/fetchPrompts',
  async (params: { page?: number; pageSize?: number; search?: string }, { rejectWithValue }) => {
    try {
      const response = await promptApi.getPrompts(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch prompts');
    }
  },
);

export const fetchPromptById = createAsyncThunk(
  'prompt/fetchPromptById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await promptApi.getPromptById(id);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch prompt');
    }
  },
);

export const createPrompt = createAsyncThunk(
  'prompt/createPrompt',
  async (promptData: Partial<Prompt>, { rejectWithValue }) => {
    try {
      const response = await promptApi.createPrompt(promptData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create prompt');
    }
  },
);

export const updatePrompt = createAsyncThunk(
  'prompt/updatePrompt',
  async ({ id, data }: { id: string; data: Partial<Prompt> }, { rejectWithValue }) => {
    try {
      const response = await promptApi.updatePrompt(id, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update prompt');
    }
  },
);

export const deletePrompt = createAsyncThunk(
  'prompt/deletePrompt',
  async (id: string, { rejectWithValue }) => {
    try {
      await promptApi.deletePrompt(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete prompt');
    }
  },
);

export const fetchPromptVersions = createAsyncThunk(
  'prompt/fetchPromptVersions',
  async (promptId: string, { rejectWithValue }) => {
    try {
      const response = await promptApi.getPromptVersions(promptId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch versions');
    }
  },
);

export const createPromptVersion = createAsyncThunk(
  'prompt/createPromptVersion',
  async (
    { promptId, data }: { promptId: string; data: Partial<PromptVersion> },
    { rejectWithValue },
  ) => {
    try {
      const response = await promptApi.createPromptVersion(promptId, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create version');
    }
  },
);

export const fetchTemplates = createAsyncThunk(
  'prompt/fetchTemplates',
  async (_, { rejectWithValue }) => {
    try {
      const response = await promptApi.getTemplates();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch templates');
    }
  },
);

// 初始状态
const initialState: PromptState = {
  prompts: [],
  currentPrompt: null,
  promptVersions: [],
  templates: [],
  isLoading: false,
  error: null,
  pagination: {
    total: 0,
    page: 1,
    pageSize: 20,
    totalPages: 0,
  },
};

// Slice
const promptSlice = createSlice({
  name: 'prompt',
  initialState,
  reducers: {
    clearError: state => {
      state.error = null;
    },
    setCurrentPrompt: (state, action: PayloadAction<Prompt | null>) => {
      state.currentPrompt = action.payload;
    },
    clearCurrentPrompt: state => {
      state.currentPrompt = null;
      state.promptVersions = [];
    },
    updatePromptInList: (state, action: PayloadAction<Prompt>) => {
      const index = state.prompts.findIndex(p => p.id === action.payload.id);
      if (index !== -1) {
        state.prompts[index] = action.payload;
      }
    },
  },
  extraReducers: builder => {
    // Fetch prompts
    builder
      .addCase(fetchPrompts.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPrompts.fulfilled, (state, action) => {
        state.isLoading = false;
        const response = action.payload as PaginatedResponse<Prompt>;
        state.prompts = response.items;
        state.pagination = {
          total: response.total,
          page: response.page,
          pageSize: response.pageSize,
          totalPages: response.totalPages,
        };
      })
      .addCase(fetchPrompts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch prompt by ID
    builder
      .addCase(fetchPromptById.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPromptById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentPrompt = action.payload;
      })
      .addCase(fetchPromptById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create prompt
    builder
      .addCase(createPrompt.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createPrompt.fulfilled, (state, action) => {
        state.isLoading = false;
        state.prompts.unshift(action.payload);
        state.currentPrompt = action.payload;
      })
      .addCase(createPrompt.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update prompt
    builder
      .addCase(updatePrompt.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updatePrompt.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.prompts.findIndex(p => p.id === action.payload.id);
        if (index !== -1) {
          state.prompts[index] = action.payload;
        }
        if (state.currentPrompt?.id === action.payload.id) {
          state.currentPrompt = action.payload;
        }
      })
      .addCase(updatePrompt.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Delete prompt
    builder
      .addCase(deletePrompt.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deletePrompt.fulfilled, (state, action) => {
        state.isLoading = false;
        state.prompts = state.prompts.filter(p => p.id !== action.payload);
        if (state.currentPrompt?.id === action.payload) {
          state.currentPrompt = null;
        }
      })
      .addCase(deletePrompt.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch prompt versions
    builder
      .addCase(fetchPromptVersions.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPromptVersions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.promptVersions = action.payload;
      })
      .addCase(fetchPromptVersions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create prompt version
    builder
      .addCase(createPromptVersion.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createPromptVersion.fulfilled, (state, action) => {
        state.isLoading = false;
        state.promptVersions.unshift(action.payload);
      })
      .addCase(createPromptVersion.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch templates
    builder
      .addCase(fetchTemplates.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTemplates.fulfilled, (state, action) => {
        state.isLoading = false;
        state.templates = action.payload;
      })
      .addCase(fetchTemplates.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setCurrentPrompt, clearCurrentPrompt, updatePromptInList } =
  promptSlice.actions;

export default promptSlice.reducer;
