import React, { Suspense } from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import { Spin } from 'antd';

import Layout from '../components/Layout';
import ProtectedRoute from '../components/auth/ProtectedRoute';
import ErrorBoundary from '../components/ErrorBoundary';

// 懒加载页面组件
const Dashboard = React.lazy(() => import('../pages/Dashboard'));
const LoginPage = React.lazy(() => import('../pages/auth/LoginPage'));
const RegisterPage = React.lazy(() => import('../pages/auth/RegisterPage'));
// const ForgotPassword = React.lazy(() => import('@/pages/Auth/ForgotPassword'));
// const ResetPassword = React.lazy(() => import('@/pages/Auth/ResetPassword'));

const PromptList = React.lazy(() => import('@/pages/Prompts/PromptList'));
const PromptDetail = React.lazy(() => import('@/pages/Prompts/PromptDetail'));
const PromptEditor = React.lazy(() => import('@/pages/Prompts/PromptEditor'));
const PromptVersions = React.lazy(() => import('@/pages/Prompts/PromptVersions'));

const TeamList = React.lazy(() => import('@/pages/Teams/TeamList'));
const TeamDetail = React.lazy(() => import('@/pages/Teams/TeamDetail'));
const TeamSettings = React.lazy(() => import('@/pages/Teams/TeamSettings'));

const TestSessions = React.lazy(() => import('@/pages/Testing/TestSessions'));
const TestDetail = React.lazy(() => import('@/pages/Testing/TestDetail'));
const TestRunner = React.lazy(() => import('@/pages/Testing/TestRunner'));

const Templates = React.lazy(() => import('@/pages/Templates/Templates'));
const TemplateEditor = React.lazy(() => import('@/pages/Templates/TemplateEditor'));

const Analytics = React.lazy(() => import('@/pages/Analytics/Analytics'));
const Settings = React.lazy(() => import('@/pages/Settings/Settings'));
const Profile = React.lazy(() => import('@/pages/Profile/Profile'));

const NotFound = React.lazy(() => import('@/pages/NotFound'));

// 加载组件
const LoadingComponent = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    height: '200px' 
  }}>
    <Spin size="large" />
  </div>
);

// 路由配置
export const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to="/dashboard" replace />,
  },
  {
    path: '/auth',
    children: [
      {
        path: 'login',
        element: (
          <ProtectedRoute requireAuth={false}>
            <ErrorBoundary>
              <Suspense fallback={<LoadingComponent />}>
                <LoginPage />
              </Suspense>
            </ErrorBoundary>
          </ProtectedRoute>
        ),
      },
      {
        path: 'register',
        element: (
          <ProtectedRoute requireAuth={false}>
            <ErrorBoundary>
              <Suspense fallback={<LoadingComponent />}>
                <RegisterPage />
              </Suspense>
            </ErrorBoundary>
          </ProtectedRoute>
        ),
      },
    ],
  },

  {
    path: '/',
    element: (
      <ProtectedRoute requireAuth={true}>
        <Layout />
      </ProtectedRoute>
    ),
    children: [
      {
        path: 'dashboard',
        element: (
          <ErrorBoundary>
            <Suspense fallback={<LoadingComponent />}>
              <Dashboard />
            </Suspense>
          </ErrorBoundary>
        ),
      },
      {
        path: 'prompts',
        children: [
          {
            index: true,
            element: (
              <ErrorBoundary>
                <Suspense fallback={<LoadingComponent />}>
                  <PromptList />
                </Suspense>
              </ErrorBoundary>
            ),
          },
          {
            path: 'new',
            element: (
              <ErrorBoundary>
                <Suspense fallback={<LoadingComponent />}>
                  <PromptEditor />
                </Suspense>
              </ErrorBoundary>
            ),
          },
          {
            path: ':id',
            element: (
              <ErrorBoundary>
                <Suspense fallback={<LoadingComponent />}>
                  <PromptDetail />
                </Suspense>
              </ErrorBoundary>
            ),
          },
          {
            path: ':id/edit',
            element: (
              <ErrorBoundary>
                <Suspense fallback={<LoadingComponent />}>
                  <PromptEditor />
                </Suspense>
              </ErrorBoundary>
            ),
          },
          {
            path: ':id/versions',
            element: (
              <ErrorBoundary>
                <Suspense fallback={<LoadingComponent />}>
                  <PromptVersions />
                </Suspense>
              </ErrorBoundary>
            ),
          },
        ],
      },
      {
        path: 'teams',
        children: [
          {
            index: true,
            element: (
              <ErrorBoundary>
                <Suspense fallback={<LoadingComponent />}>
                  <TeamList />
                </Suspense>
              </ErrorBoundary>
            ),
          },
          {
            path: ':id',
            element: (
              <ErrorBoundary>
                <Suspense fallback={<LoadingComponent />}>
                  <TeamDetail />
                </Suspense>
              </ErrorBoundary>
            ),
          },
          {
            path: ':id/settings',
            element: (
              <ErrorBoundary>
                <Suspense fallback={<LoadingComponent />}>
                  <TeamSettings />
                </Suspense>
              </ErrorBoundary>
            ),
          },
        ],
      },
      {
        path: 'testing',
        children: [
          {
            index: true,
            element: (
              <ErrorBoundary>
                <Suspense fallback={<LoadingComponent />}>
                  <TestSessions />
                </Suspense>
              </ErrorBoundary>
            ),
          },
          {
            path: 'new',
            element: (
              <ErrorBoundary>
                <Suspense fallback={<LoadingComponent />}>
                  <TestRunner />
                </Suspense>
              </ErrorBoundary>
            ),
          },
          {
            path: ':id',
            element: (
              <ErrorBoundary>
                <Suspense fallback={<LoadingComponent />}>
                  <TestDetail />
                </Suspense>
              </ErrorBoundary>
            ),
          },
        ],
      },
      {
        path: 'templates',
        children: [
          {
            index: true,
            element: (
              <ErrorBoundary>
                <Suspense fallback={<LoadingComponent />}>
                  <Templates />
                </Suspense>
              </ErrorBoundary>
            ),
          },
          {
            path: 'new',
            element: (
              <ErrorBoundary>
                <Suspense fallback={<LoadingComponent />}>
                  <TemplateEditor />
                </Suspense>
              </ErrorBoundary>
            ),
          },
          {
            path: ':id/edit',
            element: (
              <ErrorBoundary>
                <Suspense fallback={<LoadingComponent />}>
                  <TemplateEditor />
                </Suspense>
              </ErrorBoundary>
            ),
          },
        ],
      },
      {
        path: 'analytics',
        element: (
          <ErrorBoundary>
            <Suspense fallback={<LoadingComponent />}>
              <Analytics />
            </Suspense>
          </ErrorBoundary>
        ),
      },
      {
        path: 'settings',
        element: (
          <ErrorBoundary>
            <Suspense fallback={<LoadingComponent />}>
              <Settings />
            </Suspense>
          </ErrorBoundary>
        ),
      },
      {
        path: 'profile',
        element: (
          <ErrorBoundary>
            <Suspense fallback={<LoadingComponent />}>
              <Profile />
            </Suspense>
          </ErrorBoundary>
        ),
      },
    ],
  },
  {
    path: '*',
    element: (
      <ErrorBoundary>
        <Suspense fallback={<LoadingComponent />}>
          <NotFound />
        </Suspense>
      </ErrorBoundary>
    ),
  },
]);

export default router;
